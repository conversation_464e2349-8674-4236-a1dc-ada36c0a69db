using System.Collections.Generic;
using System.IO;
using System.Windows.Media;
using System.Linq;

namespace WorkingClock;

public static class FontManager
{
    private static readonly Dictionary<string, FontFamily> customFonts = new();
    private static bool fontsLoaded = false;

    public static List<string> GetAvailableFonts()
    {
        LoadCustomFonts();

        var fonts = new List<string>();
        fonts.AddRange(customFonts.Keys);
        fonts.AddRange(new[] {
            "Segoe UI", "Arial", "Consolas", "Courier New",
            "Times New Roman", "Calibri", "Microsoft YaHei", "SimSun"
        });

        return fonts;
    }

    public static FontFamily GetFontFamily(string fontName)
    {
        LoadCustomFonts();

        if (customFonts.ContainsKey(fontName))
            return customFonts[fontName];

        try
        {
            return new FontFamily(fontName);
        }
        catch
        {
            return new FontFamily("Segoe UI");
        }
    }

    private static void LoadCustomFonts()
    {
        if (fontsLoaded) return;

        try
        {
            var fontsPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Fonts");

            if (Directory.Exists(fontsPath))
            {
                foreach (var fontFile in Directory.GetFiles(fontsPath, "*.ttf"))
                {
                    try
                    {
                        var fontName = Path.GetFileNameWithoutExtension(fontFile);
                        var fontUri = new Uri($"file:///{fontFile.Replace('\\', '/')}");
                        var fontFamily = new FontFamily(fontUri, fontName);

                        if (fontName.Contains("DS-Digital"))
                            customFonts["DS-Digital"] = fontFamily;
                        else
                            customFonts[fontName] = fontFamily;
                    }
                    catch { }
                }
            }

            if (!customFonts.ContainsKey("DS-Digital"))
                customFonts["DS-Digital"] = new FontFamily("Consolas");
        }
        catch { }

        fontsLoaded = true;
    }

    public static bool IsDigitalFont(string fontName) => fontName.Contains("DS-Digital");

    public static string GetFontDisplayName(string fontName) => fontName switch
    {
        "DS-Digital" => "DS-Digital (LED数字)",
        "Segoe UI" => "Segoe UI (默认)",
        "Consolas" => "Consolas (等宽)",
        "Arial" => "Arial (经典)",
        "Microsoft YaHei" => "Microsoft YaHei (微软雅黑)",
        _ => fontName
    };

    public static string GetFontDownloadHint(string fontName) =>
        fontName == "DS-Digital" && !customFonts.ContainsKey("DS-Digital")
            ? "提示：使用Consolas字体作为DS-Digital的替代。"
            : string.Empty;

    public static List<string> SearchFonts(string keyword)
    {
        var fonts = GetAvailableFonts();
        return string.IsNullOrWhiteSpace(keyword)
            ? fonts
            : fonts.Where(f => f.ToLower().Contains(keyword.ToLower())).ToList();
    }

    public static Dictionary<string, int> GetFontStatistics() => new()
    {
        ["系统字体总数"] = 8,
        ["自定义字体总数"] = customFonts.Count,
        ["等宽字体数量"] = 2,
        ["数字字体数量"] = customFonts.ContainsKey("DS-Digital") ? 1 : 0,
        ["中文字体数量"] = 2
    };
}