{"version": 3, "targets": {"net6.0-windows7.0": {"Microsoft.WindowsAPICodePack-Core/1.1.0": {"type": "package", "compile": {"lib/Microsoft.WindowsAPICodePack.dll": {}}, "runtime": {"lib/Microsoft.WindowsAPICodePack.dll": {}}}, "Microsoft.WindowsAPICodePack-Shell/1.1.0": {"type": "package", "dependencies": {"Microsoft.WindowsAPICodePack-Core": "1.1.0"}, "compile": {"lib/Microsoft.WindowsAPICodePack.Shell.dll": {}, "lib/Microsoft.WindowsAPICodePack.ShellExtensions.dll": {}}, "runtime": {"lib/Microsoft.WindowsAPICodePack.Shell.dll": {}, "lib/Microsoft.WindowsAPICodePack.ShellExtensions.dll": {}}}}}, "libraries": {"Microsoft.WindowsAPICodePack-Core/1.1.0": {"sha512": "jU5Y9fvuC/USod2RCCHhs1PJ/T5ANBIXPhNpew53s5SDfRzvIE97XcIt9U8Wm+DF0bTJAdF9v9PD0cgbTi7uMg==", "type": "package", "path": "microsoft.windowsapicodepack-core/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/Microsoft.WindowsAPICodePack.XML", "lib/Microsoft.WindowsAPICodePack.dll", "lib/Microsoft.WindowsAPICodePack.pdb", "microsoft.windowsapicodepack-core.1.1.0.nupkg.sha512", "microsoft.windowsapicodepack-core.nuspec"]}, "Microsoft.WindowsAPICodePack-Shell/1.1.0": {"sha512": "8WvLG0TNTylzMB8gnZeZDG68/DsagAF64K35RC2Y/zocHp4e03Uj/kba6egQlq1+0uVv04FpLG+CmENEIN53rg==", "type": "package", "path": "microsoft.windowsapicodepack-shell/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/Microsoft.WindowsAPICodePack.Shell.dll", "lib/Microsoft.WindowsAPICodePack.Shell.pdb", "lib/Microsoft.WindowsAPICodePack.Shell.xml", "lib/Microsoft.WindowsAPICodePack.ShellExtensions.XML", "lib/Microsoft.WindowsAPICodePack.ShellExtensions.dll", "lib/Microsoft.WindowsAPICodePack.ShellExtensions.pdb", "microsoft.windowsapicodepack-shell.1.1.0.nupkg.sha512", "microsoft.windowsapicodepack-shell.nuspec"]}}, "projectFileDependencyGroups": {"net6.0-windows7.0": ["Microsoft.WindowsAPICodePack-Core >= 1.1.0", "Microsoft.WindowsAPICodePack-Shell >= 1.1.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "D:\\visual studio\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\time\\DigitalClock.vbproj", "projectName": "DigitalClock", "projectPath": "C:\\Users\\<USER>\\Desktop\\time\\DigitalClock.vbproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\time\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\visual studio\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"Microsoft.WindowsAPICodePack-Core": {"target": "Package", "version": "[1.1.0, )"}, "Microsoft.WindowsAPICodePack-Shell": {"target": "Package", "version": "[1.1.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1701", "level": "Warning", "warningLevel": 1, "message": "已使用“.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1”而不是项目目标框架“net6.0-windows7.0”还原包“Microsoft.WindowsAPICodePack-Core 1.1.0”。此包可能与项目不完全兼容。", "libraryId": "Microsoft.WindowsAPICodePack-Core", "targetGraphs": ["net6.0-windows7.0"]}, {"code": "NU1701", "level": "Warning", "warningLevel": 1, "message": "已使用“.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1”而不是项目目标框架“net6.0-windows7.0”还原包“Microsoft.WindowsAPICodePack-Shell 1.1.0”。此包可能与项目不完全兼容。", "libraryId": "Microsoft.WindowsAPICodePack-Shell", "targetGraphs": ["net6.0-windows7.0"]}]}