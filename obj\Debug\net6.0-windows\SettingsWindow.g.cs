﻿#pragma checksum "..\..\..\SettingsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "4F1DC56D114007EE8B6699B2C01F5A995924216F"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DigitalClock {
    
    
    /// <summary>
    /// SettingsWindow
    /// </summary>
    public partial class SettingsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 37 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FontFamilyComboBox;
        
        #line default
        #line hidden
        
        
        #line 44 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider FontSizeSlider;
        
        #line default
        #line hidden
        
        
        #line 50 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FontSizeText;
        
        #line default
        #line hidden
        
        
        #line 59 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FontColorComboBox;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle ColorPreview;
        
        #line default
        #line hidden
        
        
        #line 79 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider OpacitySlider;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OpacityText;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox TopMostCheckBox;
        
        #line default
        #line hidden
        
        
        #line 98 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoStartCheckBox;
        
        #line default
        #line hidden
        
        
        #line 109 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton Hour12RadioButton;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton Hour24RadioButton;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PreviewTextBlock;
        
        #line default
        #line hidden
        
        
        #line 146 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetButton;
        
        #line default
        #line hidden
        
        
        #line 153 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ApplyButton;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OkButton;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleClock;V1.0.0.0;component/settingswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\SettingsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.FontFamilyComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 39 "..\..\..\SettingsWindow.xaml"
            this.FontFamilyComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.FontFamilyComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.FontSizeSlider = ((System.Windows.Controls.Slider)(target));
            
            #line 49 "..\..\..\SettingsWindow.xaml"
            this.FontSizeSlider.ValueChanged += new System.Windows.RoutedPropertyChangedEventHandler<double>(this.FontSizeSlider_ValueChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.FontSizeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.FontColorComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 61 "..\..\..\SettingsWindow.xaml"
            this.FontColorComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.FontColorComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.ColorPreview = ((System.Windows.Shapes.Rectangle)(target));
            return;
            case 6:
            this.OpacitySlider = ((System.Windows.Controls.Slider)(target));
            
            #line 84 "..\..\..\SettingsWindow.xaml"
            this.OpacitySlider.ValueChanged += new System.Windows.RoutedPropertyChangedEventHandler<double>(this.OpacitySlider_ValueChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.OpacityText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.TopMostCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 95 "..\..\..\SettingsWindow.xaml"
            this.TopMostCheckBox.Checked += new System.Windows.RoutedEventHandler(this.TopMostCheckBox_Checked);
            
            #line default
            #line hidden
            
            #line 96 "..\..\..\SettingsWindow.xaml"
            this.TopMostCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.TopMostCheckBox_Unchecked);
            
            #line default
            #line hidden
            return;
            case 9:
            this.AutoStartCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 101 "..\..\..\SettingsWindow.xaml"
            this.AutoStartCheckBox.Checked += new System.Windows.RoutedEventHandler(this.AutoStartCheckBox_Checked);
            
            #line default
            #line hidden
            
            #line 102 "..\..\..\SettingsWindow.xaml"
            this.AutoStartCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.AutoStartCheckBox_Unchecked);
            
            #line default
            #line hidden
            return;
            case 10:
            this.Hour12RadioButton = ((System.Windows.Controls.RadioButton)(target));
            
            #line 113 "..\..\..\SettingsWindow.xaml"
            this.Hour12RadioButton.Checked += new System.Windows.RoutedEventHandler(this.Hour12RadioButton_Checked);
            
            #line default
            #line hidden
            return;
            case 11:
            this.Hour24RadioButton = ((System.Windows.Controls.RadioButton)(target));
            
            #line 119 "..\..\..\SettingsWindow.xaml"
            this.Hour24RadioButton.Checked += new System.Windows.RoutedEventHandler(this.Hour24RadioButton_Checked);
            
            #line default
            #line hidden
            return;
            case 12:
            this.PreviewTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.ResetButton = ((System.Windows.Controls.Button)(target));
            
            #line 151 "..\..\..\SettingsWindow.xaml"
            this.ResetButton.Click += new System.Windows.RoutedEventHandler(this.ResetButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.ApplyButton = ((System.Windows.Controls.Button)(target));
            
            #line 158 "..\..\..\SettingsWindow.xaml"
            this.ApplyButton.Click += new System.Windows.RoutedEventHandler(this.ApplyButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.OkButton = ((System.Windows.Controls.Button)(target));
            
            #line 165 "..\..\..\SettingsWindow.xaml"
            this.OkButton.Click += new System.Windows.RoutedEventHandler(this.OkButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 171 "..\..\..\SettingsWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

