using System;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using System.Globalization;

namespace WorkingClock;

/// <summary>
/// 天气数据模型
/// </summary>
public class WeatherData
{
    public double Temperature { get; set; }
    public string Description { get; set; } = "";
    public int WeatherCode { get; set; }
    public double Humidity { get; set; }
    public double WindSpeed { get; set; }
    public string Location { get; set; } = "";
    public string Icon { get; set; } = "";
}

/// <summary>
/// 位置信息模型
/// </summary>
public class LocationData
{
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public string City { get; set; } = "";
    public string Country { get; set; } = "";
}

/// <summary>
/// 天气服务类 - 使用免费的Open-Meteo API
/// </summary>
public class WeatherService
{
    private static readonly HttpClient httpClient = new HttpClient();
    
    static WeatherService()
    {
        httpClient.DefaultRequestHeaders.Add("User-Agent", "DigitalClock/1.0");
    }

    /// <summary>
    /// 通过IP自动获取位置信息
    /// </summary>
    public static async Task<LocationData?> GetLocationAsync()
    {
        try
        {
            // 使用免费的IP地理位置API
            var response = await httpClient.GetStringAsync("http://ip-api.com/json/?fields=lat,lon,city,country");
            var locationJson = JsonDocument.Parse(response);
            var root = locationJson.RootElement;

            return new LocationData
            {
                Latitude = root.GetProperty("lat").GetDouble(),
                Longitude = root.GetProperty("lon").GetDouble(),
                City = root.GetProperty("city").GetString() ?? "",
                Country = root.GetProperty("country").GetString() ?? ""
            };
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"获取位置失败: {ex.Message}");
            // 返回默认位置（北京）
            return new LocationData
            {
                Latitude = 39.9042,
                Longitude = 116.4074,
                City = "北京",
                Country = "中国"
            };
        }
    }

    /// <summary>
    /// 获取天气信息
    /// </summary>
    public static async Task<WeatherData?> GetWeatherAsync(double latitude, double longitude, string city = "")
    {
        try
        {
            // 使用Open-Meteo免费API
            var url = $"https://api.open-meteo.com/v1/forecast?latitude={latitude.ToString(CultureInfo.InvariantCulture)}&longitude={longitude.ToString(CultureInfo.InvariantCulture)}&current=temperature_2m,relative_humidity_2m,weather_code,wind_speed_10m&timezone=auto";
            
            var response = await httpClient.GetStringAsync(url);
            var weatherJson = JsonDocument.Parse(response);
            var root = weatherJson.RootElement;
            var current = root.GetProperty("current");

            var weatherCode = current.GetProperty("weather_code").GetInt32();
            var temperature = current.GetProperty("temperature_2m").GetDouble();
            var humidity = current.GetProperty("relative_humidity_2m").GetDouble();
            var windSpeed = current.GetProperty("wind_speed_10m").GetDouble();

            return new WeatherData
            {
                Temperature = temperature,
                Description = GetWeatherDescription(weatherCode),
                WeatherCode = weatherCode,
                Humidity = humidity,
                WindSpeed = windSpeed,
                Location = city,
                Icon = GetWeatherIcon(weatherCode)
            };
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"获取天气失败: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 根据天气代码获取天气描述
    /// </summary>
    private static string GetWeatherDescription(int weatherCode)
    {
        return weatherCode switch
        {
            0 => "晴朗",
            1 => "基本晴朗",
            2 => "部分多云",
            3 => "阴天",
            45 => "雾",
            48 => "雾凇",
            51 => "小雨",
            53 => "中雨",
            55 => "大雨",
            56 => "冻雨",
            57 => "强冻雨",
            61 => "小雨",
            63 => "中雨",
            65 => "大雨",
            66 => "冻雨",
            67 => "强冻雨",
            71 => "小雪",
            73 => "中雪",
            75 => "大雪",
            77 => "雪粒",
            80 => "阵雨",
            81 => "中阵雨",
            82 => "强阵雨",
            85 => "阵雪",
            86 => "强阵雪",
            95 => "雷暴",
            96 => "雷暴伴冰雹",
            99 => "强雷暴伴冰雹",
            _ => "未知"
        };
    }

    /// <summary>
    /// 根据天气代码获取天气图标
    /// </summary>
    private static string GetWeatherIcon(int weatherCode)
    {
        return weatherCode switch
        {
            0 => "☀️", // 晴朗
            1 => "🌤️", // 基本晴朗
            2 => "⛅", // 部分多云
            3 => "☁️", // 阴天
            45 or 48 => "🌫️", // 雾
            51 or 53 or 55 or 61 or 63 or 65 => "🌧️", // 雨
            56 or 57 or 66 or 67 => "🌨️", // 冻雨
            71 or 73 or 75 or 77 => "❄️", // 雪
            80 or 81 or 82 => "🌦️", // 阵雨
            85 or 86 => "🌨️", // 阵雪
            95 or 96 or 99 => "⛈️", // 雷暴
            _ => "🌡️" // 未知
        };
    }

    /// <summary>
    /// 获取完整的天气信息（自动定位）
    /// </summary>
    public static async Task<WeatherData?> GetCurrentWeatherAsync()
    {
        try
        {
            var location = await GetLocationAsync();
            if (location == null) return null;

            var weather = await GetWeatherAsync(location.Latitude, location.Longitude, location.City);
            if (weather != null)
            {
                weather.Location = $"{location.City}, {location.Country}";
            }
            return weather;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"获取天气信息失败: {ex.Message}");
            return null;
        }
    }
}
