@echo off
echo ========================================
echo    编译优化版数字时钟 (目标: <5MB)
echo ========================================
echo.

REM 检查.NET SDK
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到 .NET 6 SDK
    pause
    exit /b 1
)

if not exist "WorkingClock" (
    echo 错误: WorkingClock 项目不存在
    pause
    exit /b 1
)

cd WorkingClock

echo 🔧 正在清理项目...
dotnet clean --configuration Release

echo.
echo 📦 正在编译优化版本...
echo 优化选项:
echo • 代码修剪 (Trimming)
echo • 单文件发布
echo • 压缩启用
echo • 移除调试信息
echo • ReadyToRun 禁用
echo.

REM 优化编译命令
dotnet publish ^
  --configuration Release ^
  --runtime win-x64 ^
  --self-contained true ^
  --output "../DigitalClock-Optimized" ^
  /p:PublishSingleFile=true ^
  /p:PublishTrimmed=true ^
  /p:TrimMode=link ^
  /p:PublishReadyToRun=false ^
  /p:IncludeNativeLibrariesForSelfExtract=true ^
  /p:EnableCompressionInSingleFile=true ^
  /p:DebugType=none ^
  /p:DebugSymbols=false ^
  /p:OptimizationPreference=Size ^
  /p:IlcOptimizationPreference=Size

cd ..

if %errorlevel% neq 0 (
    echo.
    echo ❌ 编译失败！
    pause
    exit /b 1
)

echo.
echo ✅ 优化编译成功！
echo.

if exist "DigitalClock-Optimized\WorkingClock.exe" (
    echo 📊 文件大小分析:
    for %%I in ("DigitalClock-Optimized\WorkingClock.exe") do (
        set /a sizeBytes=%%~zI
        set /a sizeKB=%%~zI/1024
        set /a sizeMB=%%~zI/1048576
        echo    原始大小: %%~zI 字节
        echo    KB大小: !sizeKB! KB
        echo    MB大小: !sizeMB! MB
        
        if !sizeMB! LEQ 5 (
            echo    ✅ 目标达成: 文件大小 ≤ 5MB
        ) else (
            echo    ⚠️  文件仍然较大: !sizeMB! MB
        )
    )
    
    echo.
    echo 📂 输出目录: DigitalClock-Optimized\
    echo 📄 主程序: WorkingClock.exe
    echo.
    echo 🚀 优化特性:
    echo • 单文件部署
    echo • 代码修剪
    echo • 压缩启用
    echo • 无调试信息
    echo • 大小优化
    echo.
    
    set /p runNow="是否现在运行优化版本？(y/n): "
    if /i "%runNow%"=="y" (
        echo.
        echo 🎉 正在启动优化版数字时钟...
        start "" "DigitalClock-Optimized\WorkingClock.exe"
    )
) else (
    echo ❌ 未找到编译后的文件
)

echo.
echo 💡 进一步优化建议:
echo • 移除不必要的功能模块
echo • 简化动画效果
echo • 减少字体支持
echo • 使用更轻量的UI框架
echo.
pause
