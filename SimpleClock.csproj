<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <AssemblyTitle>极简数字时钟</AssemblyTitle>
    <AssemblyDescription>Windows 11极简数字时钟应用</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <EnableDefaultPageItems>false</EnableDefaultPageItems>
  </PropertyGroup>

  <ItemGroup>
    <ApplicationDefinition Include="CSharpApp.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </ApplicationDefinition>
    <Page Include="CSharpMainWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
  </ItemGroup>



  <!-- 排除VB.NET文件 -->
  <ItemGroup>
    <None Remove="*.vb" />
    <None Remove="App.xaml" />
    <None Remove="MainWindow.xaml" />
    <None Remove="SettingsWindow.xaml" />
  </ItemGroup>

</Project>
