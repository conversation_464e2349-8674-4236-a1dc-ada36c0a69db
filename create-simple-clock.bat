@echo off
echo Creating simple digital clock...

REM Check if .NET 6 is installed
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: .NET 6 SDK not found
    pause
    exit /b 1
)

REM Create new WPF project
if exist "WorkingClock" rmdir /s /q "WorkingClock"
dotnet new wpf -n WorkingClock -f net6.0

if %errorlevel% neq 0 (
    echo Failed to create project!
    pause
    exit /b 1
)

echo Project created successfully!
echo.
echo To run the clock:
echo 1. cd WorkingClock
echo 2. dotnet run
echo.
echo The basic WPF project is ready. You can customize it further.
pause
