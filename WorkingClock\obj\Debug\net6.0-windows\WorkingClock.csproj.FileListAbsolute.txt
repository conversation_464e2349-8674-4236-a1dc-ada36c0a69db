C:\Users\<USER>\Desktop\time\WorkingClock\bin\Debug\net6.0-windows\WorkingClock.exe
C:\Users\<USER>\Desktop\time\WorkingClock\bin\Debug\net6.0-windows\WorkingClock.deps.json
C:\Users\<USER>\Desktop\time\WorkingClock\bin\Debug\net6.0-windows\WorkingClock.runtimeconfig.json
C:\Users\<USER>\Desktop\time\WorkingClock\bin\Debug\net6.0-windows\WorkingClock.dll
C:\Users\<USER>\Desktop\time\WorkingClock\bin\Debug\net6.0-windows\WorkingClock.pdb
C:\Users\<USER>\Desktop\time\WorkingClock\obj\Debug\net6.0-windows\MainWindow.g.cs
C:\Users\<USER>\Desktop\time\WorkingClock\obj\Debug\net6.0-windows\App.g.cs
C:\Users\<USER>\Desktop\time\WorkingClock\obj\Debug\net6.0-windows\WorkingClock_MarkupCompile.cache
C:\Users\<USER>\Desktop\time\WorkingClock\obj\Debug\net6.0-windows\WorkingClock_MarkupCompile.lref
C:\Users\<USER>\Desktop\time\WorkingClock\obj\Debug\net6.0-windows\MainWindow.baml
C:\Users\<USER>\Desktop\time\WorkingClock\obj\Debug\net6.0-windows\WorkingClock.g.resources
C:\Users\<USER>\Desktop\time\WorkingClock\obj\Debug\net6.0-windows\WorkingClock.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\Desktop\time\WorkingClock\obj\Debug\net6.0-windows\WorkingClock.AssemblyInfoInputs.cache
C:\Users\<USER>\Desktop\time\WorkingClock\obj\Debug\net6.0-windows\WorkingClock.AssemblyInfo.cs
C:\Users\<USER>\Desktop\time\WorkingClock\obj\Debug\net6.0-windows\WorkingClock.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Desktop\time\WorkingClock\obj\Debug\net6.0-windows\WorkingClock.dll
C:\Users\<USER>\Desktop\time\WorkingClock\obj\Debug\net6.0-windows\refint\WorkingClock.dll
C:\Users\<USER>\Desktop\time\WorkingClock\obj\Debug\net6.0-windows\WorkingClock.pdb
C:\Users\<USER>\Desktop\time\WorkingClock\obj\Debug\net6.0-windows\WorkingClock.genruntimeconfig.cache
C:\Users\<USER>\Desktop\time\WorkingClock\obj\Debug\net6.0-windows\ref\WorkingClock.dll
