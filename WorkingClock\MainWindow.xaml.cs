using System;
using System.Windows;
using System.Windows.Input;
using System.Windows.Threading;
using System.Windows.Media;
using System.Windows.Media.Effects;
using System.Windows.Controls;
using System.Threading.Tasks;

namespace WorkingClock;

/// <summary>
/// 极简数字时钟主窗口
/// </summary>
public partial class MainWindow : Window
{
    private DispatcherTimer clockTimer = null!;
    private DispatcherTimer weatherTimer = null!;
    private bool is24HourFormat = true;
    private bool showWeather = true;
    private SettingsWindow? settingsWindow;
    private WeatherData? currentWeather;
    private AnimationManager.AnimationType currentAnimationType = AnimationManager.AnimationType.FlipPage;
    private string lastTimeText = "";
    private bool animationsEnabled = true;

    public MainWindow()
    {
        InitializeComponent();
        InitializeClock();
        InitializeWeather();
        ApplyDefaultFont();
    }

    /// <summary>
    /// 初始化时钟
    /// </summary>
    private void InitializeClock()
    {
        clockTimer = new DispatcherTimer();
        clockTimer.Interval = TimeSpan.FromSeconds(1);
        clockTimer.Tick += ClockTimer_Tick;
        clockTimer.Start();
        UpdateTimeDisplay();
    }

    /// <summary>
    /// 初始化天气
    /// </summary>
    private void InitializeWeather()
    {
        // 创建天气更新定时器（每30分钟更新一次）
        weatherTimer = new DispatcherTimer();
        weatherTimer.Interval = TimeSpan.FromMinutes(30);
        weatherTimer.Tick += WeatherTimer_Tick;
        weatherTimer.Start();

        // 立即获取一次天气
        _ = UpdateWeatherAsync();
    }

    /// <summary>
    /// 天气定时器事件
    /// </summary>
    private async void WeatherTimer_Tick(object? sender, EventArgs e)
    {
        await UpdateWeatherAsync();
    }

    /// <summary>
    /// 异步更新天气信息
    /// </summary>
    private async Task UpdateWeatherAsync()
    {
        try
        {
            currentWeather = await WeatherService.GetCurrentWeatherAsync();

            // 在UI线程上更新界面
            Dispatcher.Invoke(() =>
            {
                UpdateWeatherDisplay();
            });
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"更新天气失败: {ex.Message}");

            // 在UI线程上显示错误状态
            Dispatcher.Invoke(() =>
            {
                WeatherDescriptionTextBlock.Text = "获取失败";
                TemperatureTextBlock.Text = "--°C";
                WeatherIconTextBlock.Text = "❌";
            });
        }
    }

    /// <summary>
    /// 更新天气显示
    /// </summary>
    private void UpdateWeatherDisplay()
    {
        if (currentWeather != null && showWeather)
        {
            WeatherIconTextBlock.Text = currentWeather.Icon;
            TemperatureTextBlock.Text = $"{currentWeather.Temperature:F0}°C";
            WeatherDescriptionTextBlock.Text = currentWeather.Description;
            LocationTextBlock.Text = currentWeather.Location;

            WeatherPanel.Visibility = Visibility.Visible;
            LocationTextBlock.Visibility = string.IsNullOrEmpty(currentWeather.Location) ? Visibility.Collapsed : Visibility.Visible;
        }
        else
        {
            WeatherPanel.Visibility = Visibility.Collapsed;
            LocationTextBlock.Visibility = Visibility.Collapsed;
        }

        // 更新菜单项文本
        ShowWeatherMenuItem.Header = showWeather ? "🌤️ 隐藏天气" : "🌤️ 显示天气";
    }

    /// <summary>
    /// 应用默认字体设置
    /// </summary>
    private void ApplyDefaultFont()
    {
        try
        {
            // 设置默认的DS-Digital字体
            TimeTextBlock.FontFamily = FontManager.GetFontFamily("DS-Digital");
            TimeTextBlock.Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("LimeGreen"));
            TimeTextBlock.FontWeight = FontWeights.Normal;

            // 增强阴影效果以配合LED字体
            if (TimeTextBlock.Effect is DropShadowEffect shadow)
            {
                shadow.Opacity = 0.7;
                shadow.BlurRadius = 6;
                shadow.Color = Colors.Black;
            }

            // 显示字体提示（如果需要）
            var fontHint = FontManager.GetFontDownloadHint("DS-Digital");
            if (!string.IsNullOrEmpty(fontHint))
            {
                System.Diagnostics.Debug.WriteLine(fontHint);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"应用默认字体失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 时钟定时器事件
    /// </summary>
    private void ClockTimer_Tick(object? sender, EventArgs e)
    {
        UpdateTimeDisplay();
    }

    /// <summary>
    /// 更新时间显示
    /// </summary>
    private void UpdateTimeDisplay()
    {
        var now = DateTime.Now;
        var newTimeText = is24HourFormat ? now.ToString("HH:mm:ss") : now.ToString("hh:mm:ss tt");

        // 如果时间文本发生变化且启用动画，则播放动画
        if (animationsEnabled && newTimeText != lastTimeText && !string.IsNullOrEmpty(lastTimeText))
        {
            AnimationManager.ApplyAnimation(currentAnimationType, TimeTextBlock, newTimeText,
                text => TimeTextBlock.Text = text);
        }
        else
        {
            TimeTextBlock.Text = newTimeText;
        }

        lastTimeText = newTimeText;

        // 更新状态文本
        if (StatusText != null)
        {
            StatusText.Text = is24HourFormat ? "24H" : "12H";
        }

        // 更新日期
        DateTextBlock.Text = now.ToString("yyyy年MM月dd日 dddd");
    }

    /// <summary>
    /// 窗口拖动事件
    /// </summary>
    private void Border_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
    {
        if (e.ButtonState == MouseButtonState.Pressed)
        {
            this.DragMove();
        }
    }

    /// <summary>
    /// 设置菜单点击事件
    /// </summary>
    private void SettingsMenuItem_Click(object sender, RoutedEventArgs e)
    {
        ShowSettingsWindow();
    }

    /// <summary>
    /// 显示/隐藏天气菜单点击事件
    /// </summary>
    private void ShowWeatherMenuItem_Click(object sender, RoutedEventArgs e)
    {
        showWeather = !showWeather;
        UpdateWeatherDisplay();
    }

    /// <summary>
    /// 刷新天气菜单点击事件
    /// </summary>
    private async void RefreshWeatherMenuItem_Click(object sender, RoutedEventArgs e)
    {
        WeatherDescriptionTextBlock.Text = "刷新中...";
        await UpdateWeatherAsync();
    }

    /// <summary>
    /// 动画效果菜单点击事件
    /// </summary>
    private void AnimationMenuItem_Click(object sender, RoutedEventArgs e)
    {
        ShowAnimationSelectionDialog();
    }

    /// <summary>
    /// 显示设置窗口
    /// </summary>
    private void ShowSettingsWindow()
    {
        if (settingsWindow == null || !settingsWindow.IsLoaded)
        {
            settingsWindow = new SettingsWindow();
            settingsWindow.Owner = this;
            settingsWindow.SettingsChanged += OnSettingsChanged;
        }
        settingsWindow.Show();
        settingsWindow.Activate();
    }

    /// <summary>
    /// 设置更改事件处理
    /// </summary>
    private void OnSettingsChanged()
    {
        if (settingsWindow != null)
        {
            ApplySettings(settingsWindow);
        }
    }

    /// <summary>
    /// 应用设置到主窗口
    /// </summary>
    private void ApplySettings(SettingsWindow settings)
    {
        // 应用边框设置
        MainBorder.CornerRadius = new CornerRadius(settings.BorderRadius);
        MainBorder.BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString(settings.BorderColor));

        // 应用背景设置
        var bgColor = (Color)ColorConverter.ConvertFromString(settings.BackgroundColor);
        bgColor.A = (byte)(settings.BackgroundOpacity * 255);
        MainBorder.Background = new SolidColorBrush(bgColor);

        // 应用字体设置 - 使用FontManager
        TimeTextBlock.FontFamily = FontManager.GetFontFamily(settings.FontFamily);
        TimeTextBlock.FontSize = settings.FontSize;
        TimeTextBlock.Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString(settings.FontColor));
        TimeTextBlock.FontWeight = settings.FontWeight;

        // 应用日期字体设置
        DateTextBlock.FontFamily = FontManager.GetFontFamily(settings.FontFamily);
        DateTextBlock.Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString(settings.FontColor));

        // 应用天气字体设置
        if (WeatherIconTextBlock != null)
            WeatherIconTextBlock.FontFamily = FontManager.GetFontFamily(settings.FontFamily);
        if (TemperatureTextBlock != null)
        {
            TemperatureTextBlock.FontFamily = FontManager.GetFontFamily(settings.FontFamily);
            TemperatureTextBlock.Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString(settings.FontColor));
        }
        if (WeatherDescriptionTextBlock != null)
        {
            WeatherDescriptionTextBlock.FontFamily = FontManager.GetFontFamily(settings.FontFamily);
            WeatherDescriptionTextBlock.Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString(settings.FontColor));
        }
        if (LocationTextBlock != null)
        {
            LocationTextBlock.FontFamily = FontManager.GetFontFamily(settings.FontFamily);
            LocationTextBlock.Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString(settings.FontColor));
        }

        // 应用阴影设置
        var timeEffect = new DropShadowEffect
        {
            Color = Colors.Black,
            Direction = 270,
            ShadowDepth = 2,
            BlurRadius = settings.ShadowBlur,
            Opacity = settings.ShadowOpacity
        };
        TimeTextBlock.Effect = timeEffect;

        var dateEffect = new DropShadowEffect
        {
            Color = Colors.Black,
            Direction = 270,
            ShadowDepth = 1,
            BlurRadius = settings.ShadowBlur * 0.5,
            Opacity = settings.ShadowOpacity * 0.6
        };
        DateTextBlock.Effect = dateEffect;

        // 应用窗口大小设置
        var sizeParts = settings.WindowSize.Split(',');
        if (sizeParts.Length == 2 &&
            double.TryParse(sizeParts[0], out double width) &&
            double.TryParse(sizeParts[1], out double height))
        {
            this.Width = width;
            this.Height = height;
        }

        // 应用窗口位置设置
        ApplyWindowPosition(settings.WindowPosition);
    }

    /// <summary>
    /// 应用窗口位置
    /// </summary>
    private void ApplyWindowPosition(string position)
    {
        var screenWidth = SystemParameters.PrimaryScreenWidth;
        var screenHeight = SystemParameters.PrimaryScreenHeight;

        switch (position)
        {
            case "Center":
                this.WindowStartupLocation = WindowStartupLocation.CenterScreen;
                this.Left = (screenWidth - this.Width) / 2;
                this.Top = (screenHeight - this.Height) / 2;
                break;
            case "TopLeft":
                this.Left = 50;
                this.Top = 50;
                break;
            case "TopRight":
                this.Left = screenWidth - this.Width - 50;
                this.Top = 50;
                break;
            case "BottomLeft":
                this.Left = 50;
                this.Top = screenHeight - this.Height - 100;
                break;
            case "BottomRight":
                this.Left = screenWidth - this.Width - 50;
                this.Top = screenHeight - this.Height - 100;
                break;
        }
    }

    /// <summary>
    /// 置顶菜单点击事件
    /// </summary>
    private void TopMostMenuItem_Click(object sender, RoutedEventArgs e)
    {
        this.Topmost = !this.Topmost;
        TopMostMenuItem.Header = this.Topmost ? "📌 取消置顶" : "📌 置顶";
    }

    /// <summary>
    /// 12小时制菜单点击事件
    /// </summary>
    private void Hour12MenuItem_Click(object sender, RoutedEventArgs e)
    {
        is24HourFormat = false;
        UpdateTimeDisplay();
    }

    /// <summary>
    /// 24小时制菜单点击事件
    /// </summary>
    private void Hour24MenuItem_Click(object sender, RoutedEventArgs e)
    {
        is24HourFormat = true;
        UpdateTimeDisplay();
    }

    /// <summary>
    /// 关于菜单点击事件
    /// </summary>
    private void AboutMenuItem_Click(object sender, RoutedEventArgs e)
    {
        var aboutText = "极简数字时钟 v2.0\nWindows 11 现代化数字时钟应用\n\n" +
                       "🕐 功能特色：\n" +
                       "• 实时时间显示\n" +
                       "• 12/24小时制切换\n" +
                       "• 🌤️ 免费天气预报（自动定位）\n" +
                       "• ⚙️ 完全个性化设置\n" +
                       "• 📌 窗口置顶功能\n" +
                       "• 🖱️ 拖动移动窗口\n" +
                       "• 🎨 半透明现代化界面\n\n" +
                       "🌐 天气数据来源：\n" +
                       "• Open-Meteo API (开源免费)\n" +
                       "• IP-API 地理定位服务\n\n" +
                       "💡 右键点击可访问更多功能！";

        MessageBox.Show(aboutText,
                       "关于 - 极简数字时钟",
                       MessageBoxButton.OK,
                       MessageBoxImage.Information);
    }

    /// <summary>
    /// 退出菜单点击事件
    /// </summary>
    private void ExitMenuItem_Click(object sender, RoutedEventArgs e)
    {
        Application.Current.Shutdown();
    }

    /// <summary>
    /// 显示动画选择对话框
    /// </summary>
    private void ShowAnimationSelectionDialog()
    {
        var dialog = new Window
        {
            Title = "选择动画效果",
            Width = 400,
            Height = 350,
            WindowStartupLocation = WindowStartupLocation.CenterOwner,
            Owner = this,
            ResizeMode = ResizeMode.NoResize,
            Background = new SolidColorBrush(Color.FromArgb(240, 30, 30, 30)),
            Foreground = new SolidColorBrush(Colors.White)
        };

        var mainPanel = new StackPanel
        {
            Margin = new Thickness(20)
        };

        // 标题
        var titleText = new TextBlock
        {
            Text = "🎬 选择时间切换动画效果",
            FontSize = 16,
            FontWeight = FontWeights.Bold,
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 0, 0, 20)
        };
        mainPanel.Children.Add(titleText);

        // 动画选项
        var animationOptions = new[]
        {
            new { Type = AnimationManager.AnimationType.FlipPage, Name = "📖 翻页效果", Description = "像翻书一样的3D翻转效果" },
            new { Type = AnimationManager.AnimationType.Slide, Name = "📱 滑动效果", Description = "上下滑动切换效果" },
            new { Type = AnimationManager.AnimationType.Fade, Name = "🌟 淡入淡出", Description = "渐变透明度切换效果" },
            new { Type = AnimationManager.AnimationType.Scale, Name = "🔍 缩放效果", Description = "缩放变化切换效果" },
            new { Type = AnimationManager.AnimationType.Bounce, Name = "🏀 弹跳效果", Description = "弹性动画效果" }
        };

        foreach (var option in animationOptions)
        {
            var optionPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 5, 0, 0)
            };

            var radioButton = new RadioButton
            {
                Content = option.Name,
                IsChecked = currentAnimationType == option.Type,
                Margin = new Thickness(0, 0, 10, 0),
                VerticalAlignment = VerticalAlignment.Center,
                Foreground = new SolidColorBrush(Colors.White)
            };

            radioButton.Checked += (s, e) => currentAnimationType = option.Type;

            var descText = new TextBlock
            {
                Text = option.Description,
                FontSize = 11,
                Foreground = new SolidColorBrush(Colors.LightGray),
                VerticalAlignment = VerticalAlignment.Center
            };

            optionPanel.Children.Add(radioButton);
            optionPanel.Children.Add(descText);
            mainPanel.Children.Add(optionPanel);
        }

        // 启用/禁用动画
        var enablePanel = new StackPanel
        {
            Orientation = Orientation.Horizontal,
            Margin = new Thickness(0, 20, 0, 0)
        };

        var enableCheckBox = new CheckBox
        {
            Content = "🎭 启用动画效果",
            IsChecked = animationsEnabled,
            Foreground = new SolidColorBrush(Colors.White),
            Margin = new Thickness(0, 0, 10, 0)
        };

        enableCheckBox.Checked += (s, e) => animationsEnabled = true;
        enableCheckBox.Unchecked += (s, e) => animationsEnabled = false;

        enablePanel.Children.Add(enableCheckBox);
        mainPanel.Children.Add(enablePanel);

        // 按钮面板
        var buttonPanel = new StackPanel
        {
            Orientation = Orientation.Horizontal,
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 20, 0, 0)
        };

        var testButton = new Button
        {
            Content = "🎬 测试效果",
            Margin = new Thickness(0, 0, 10, 0),
            Padding = new Thickness(15, 8, 15, 8),
            Background = new SolidColorBrush(Color.FromArgb(100, 0, 120, 215)),
            Foreground = new SolidColorBrush(Colors.White),
            BorderBrush = new SolidColorBrush(Color.FromArgb(150, 0, 120, 215)),
            BorderThickness = new Thickness(1)
        };

        testButton.Click += (s, e) =>
        {
            if (animationsEnabled)
            {
                var testTime = DateTime.Now.ToString(is24HourFormat ? "HH:mm:ss" : "hh:mm:ss tt");
                AnimationManager.ApplyAnimation(currentAnimationType, TimeTextBlock, testTime,
                    text => TimeTextBlock.Text = text);
            }
        };

        var closeButton = new Button
        {
            Content = "✅ 确定",
            Padding = new Thickness(15, 8, 15, 8),
            Background = new SolidColorBrush(Color.FromArgb(100, 0, 150, 0)),
            Foreground = new SolidColorBrush(Colors.White),
            BorderBrush = new SolidColorBrush(Color.FromArgb(150, 0, 150, 0)),
            BorderThickness = new Thickness(1)
        };

        closeButton.Click += (s, e) => dialog.Close();

        buttonPanel.Children.Add(testButton);
        buttonPanel.Children.Add(closeButton);
        mainPanel.Children.Add(buttonPanel);

        dialog.Content = mainPanel;
        dialog.ShowDialog();
    }
}