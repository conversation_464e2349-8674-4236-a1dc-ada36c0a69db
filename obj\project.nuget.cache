{"version": 2, "dgSpecHash": "bko92zrVbhw=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\Desktop\\time\\DigitalClock.vbproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsapicodepack-core\\1.1.0\\microsoft.windowsapicodepack-core.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsapicodepack-shell\\1.1.0\\microsoft.windowsapicodepack-shell.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.app.ref\\6.0.36\\microsoft.netcore.app.ref.6.0.36.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsdesktop.app.ref\\6.0.36\\microsoft.windowsdesktop.app.ref.6.0.36.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.app.ref\\6.0.36\\microsoft.aspnetcore.app.ref.6.0.36.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.app.host.win-x64\\6.0.36\\microsoft.netcore.app.host.win-x64.6.0.36.nupkg.sha512"], "logs": [{"code": "NU1701", "level": "Warning", "message": "已使用“.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1”而不是项目目标框架“net6.0-windows7.0”还原包“Microsoft.WindowsAPICodePack-Core 1.1.0”。此包可能与项目不完全兼容。", "projectPath": "C:\\Users\\<USER>\\Desktop\\time\\DigitalClock.vbproj", "warningLevel": 1, "filePath": "C:\\Users\\<USER>\\Desktop\\time\\DigitalClock.vbproj", "libraryId": "Microsoft.WindowsAPICodePack-Core", "targetGraphs": ["net6.0-windows7.0"]}, {"code": "NU1701", "level": "Warning", "message": "已使用“.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1”而不是项目目标框架“net6.0-windows7.0”还原包“Microsoft.WindowsAPICodePack-Shell 1.1.0”。此包可能与项目不完全兼容。", "projectPath": "C:\\Users\\<USER>\\Desktop\\time\\DigitalClock.vbproj", "warningLevel": 1, "filePath": "C:\\Users\\<USER>\\Desktop\\time\\DigitalClock.vbproj", "libraryId": "Microsoft.WindowsAPICodePack-Shell", "targetGraphs": ["net6.0-windows7.0"]}]}