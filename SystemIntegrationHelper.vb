Imports Microsoft.Win32
Imports System.IO
Imports System.Reflection
Imports System.Windows.Input
Imports System.Runtime.InteropServices

''' <summary>
''' 系统集成帮助类
''' </summary>
Public Class SystemIntegrationHelper

    ' 注册表路径
    Private Const REGISTRY_RUN_PATH As String = "SOFTWARE\Microsoft\Windows\CurrentVersion\Run"
    Private Const APP_NAME As String = "DigitalClock"

    ' 全局热键相关
    Private Const MOD_ALT As Integer = &H1
    Private Const MOD_CONTROL As Integer = &H2
    Private Const MOD_SHIFT As Integer = &H4
    Private Const MOD_WIN As Integer = &H8
    Private Const WM_HOTKEY As Integer = &H312

    ' Windows API 声明
    <DllImport("user32.dll")>
    Private Shared Function RegisterHotKey(hWnd As IntPtr, id As Integer, fsModifiers As Integer, vk As Integer) As Boolean
    End Function

    <DllImport("user32.dll")>
    Private Shared Function UnregisterHotKey(hWnd As IntPtr, id As Integer) As Boolean
    End Function

    ''' <summary>
    ''' 设置开机自启
    ''' </summary>
    Public Shared Sub SetAutoStart(enable As Boolean)
        Try
            Using key As RegistryKey = Registry.CurrentUser.OpenSubKey(REGISTRY_RUN_PATH, True)
                If key IsNot Nothing Then
                    If enable Then
                        ' 获取当前应用程序路径
                        Dim exePath As String = Assembly.GetExecutingAssembly().Location
                        If exePath.EndsWith(".dll") Then
                            ' 如果是.dll文件，需要使用dotnet运行
                            exePath = $"dotnet ""{exePath}"""
                        End If
                        key.SetValue(APP_NAME, exePath)
                    Else
                        ' 删除自启动项
                        If key.GetValue(APP_NAME) IsNot Nothing Then
                            key.DeleteValue(APP_NAME)
                        End If
                    End If
                End If
            End Using
        Catch ex As Exception
            Throw New InvalidOperationException($"设置开机自启失败: {ex.Message}", ex)
        End Try
    End Sub

    ''' <summary>
    ''' 检查是否已设置开机自启
    ''' </summary>
    Public Shared Function IsAutoStartEnabled() As Boolean
        Try
            Using key As RegistryKey = Registry.CurrentUser.OpenSubKey(REGISTRY_RUN_PATH, False)
                If key IsNot Nothing Then
                    Return key.GetValue(APP_NAME) IsNot Nothing
                End If
            End Using
        Catch
            ' 如果读取失败，返回false
        End Try
        Return False
    End Function

    ''' <summary>
    ''' 注册全局热键
    ''' </summary>
    Public Shared Function RegisterGlobalHotKey(window As Window, hotKeyId As Integer, modifiers As ModifierKeys, key As Key) As Boolean
        Try
            Dim windowHelper As New System.Windows.Interop.WindowInteropHelper(window)
            Dim hWnd As IntPtr = windowHelper.Handle

            If hWnd = IntPtr.Zero Then
                Return False
            End If

            ' 转换修饰键
            Dim fsModifiers As Integer = 0
            If modifiers.HasFlag(ModifierKeys.Alt) Then fsModifiers = fsModifiers Or MOD_ALT
            If modifiers.HasFlag(ModifierKeys.Control) Then fsModifiers = fsModifiers Or MOD_CONTROL
            If modifiers.HasFlag(ModifierKeys.Shift) Then fsModifiers = fsModifiers Or MOD_SHIFT
            If modifiers.HasFlag(ModifierKeys.Windows) Then fsModifiers = fsModifiers Or MOD_WIN

            ' 转换虚拟键码
            Dim vk As Integer = KeyInterop.VirtualKeyFromKey(key)

            Return RegisterHotKey(hWnd, hotKeyId, fsModifiers, vk)

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"注册热键失败: {ex.Message}")
            Return False
        End Try
    End Function

    ''' <summary>
    ''' 注销全局热键
    ''' </summary>
    Public Shared Function UnregisterGlobalHotKey(window As Window, hotKeyId As Integer) As Boolean
        Try
            Dim windowHelper As New System.Windows.Interop.WindowInteropHelper(window)
            Dim hWnd As IntPtr = windowHelper.Handle

            If hWnd = IntPtr.Zero Then
                Return False
            End If

            Return UnregisterHotKey(hWnd, hotKeyId)

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"注销热键失败: {ex.Message}")
            Return False
        End Try
    End Function

    ''' <summary>
    ''' 创建桌面快捷方式
    ''' </summary>
    Public Shared Sub CreateDesktopShortcut()
        Try
            Dim desktopPath As String = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
            Dim shortcutPath As String = Path.Combine(desktopPath, $"{APP_NAME}.lnk")
            Dim exePath As String = Assembly.GetExecutingAssembly().Location

            ' 使用WScript.Shell创建快捷方式
            Dim shell As Object = CreateObject("WScript.Shell")
            Dim shortcut As Object = shell.CreateShortcut(shortcutPath)
            
            shortcut.TargetPath = exePath
            shortcut.WorkingDirectory = Path.GetDirectoryName(exePath)
            shortcut.Description = "极简数字时钟"
            shortcut.IconLocation = Path.Combine(Path.GetDirectoryName(exePath), "Resources", "clock.ico")
            shortcut.Save()

        Catch ex As Exception
            Throw New InvalidOperationException($"创建桌面快捷方式失败: {ex.Message}", ex)
        End Try
    End Sub

    ''' <summary>
    ''' 检查是否以管理员权限运行
    ''' </summary>
    Public Shared Function IsRunningAsAdministrator() As Boolean
        Try
            Dim identity As System.Security.Principal.WindowsIdentity = System.Security.Principal.WindowsIdentity.GetCurrent()
            Dim principal As New System.Security.Principal.WindowsPrincipal(identity)
            Return principal.IsInRole(System.Security.Principal.WindowsBuiltInRole.Administrator)
        Catch
            Return False
        End Try
    End Function

    ''' <summary>
    ''' 以管理员权限重启应用程序
    ''' </summary>
    Public Shared Sub RestartAsAdministrator()
        Try
            Dim exePath As String = Assembly.GetExecutingAssembly().Location
            Dim startInfo As New ProcessStartInfo()
            
            If exePath.EndsWith(".dll") Then
                startInfo.FileName = "dotnet"
                startInfo.Arguments = $"""{exePath}"""
            Else
                startInfo.FileName = exePath
            End If
            
            startInfo.UseShellExecute = True
            startInfo.Verb = "runas" ' 以管理员权限运行
            
            Process.Start(startInfo)
            Application.Current.Shutdown()

        Catch ex As Exception
            MessageBox.Show($"无法以管理员权限重启应用程序: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error)
        End Try
    End Sub

    ''' <summary>
    ''' 获取应用程序版本信息
    ''' </summary>
    Public Shared Function GetApplicationVersion() As String
        Try
            Dim assembly As Assembly = Assembly.GetExecutingAssembly()
            Dim version As Version = assembly.GetName().Version
            Return $"{version.Major}.{version.Minor}.{version.Build}"
        Catch
            Return "1.0.0"
        End Try
    End Function

    ''' <summary>
    ''' 获取应用程序安装路径
    ''' </summary>
    Public Shared Function GetApplicationPath() As String
        Try
            Return Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location)
        Catch
            Return Environment.CurrentDirectory
        End Try
    End Function

    ''' <summary>
    ''' 检查Windows版本是否支持特定功能
    ''' </summary>
    Public Shared Function IsWindows10OrLater() As Boolean
        Try
            Dim version As Version = Environment.OSVersion.Version
            Return version.Major >= 10
        Catch
            Return False
        End Try
    End Function

    ''' <summary>
    ''' 检查Windows版本是否为Windows 11
    ''' </summary>
    Public Shared Function IsWindows11() As Boolean
        Try
            Dim version As Version = Environment.OSVersion.Version
            Return version.Major >= 10 AndAlso version.Build >= 22000
        Catch
            Return False
        End Try
    End Function
End Class
