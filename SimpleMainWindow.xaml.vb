Imports System.Windows
Imports System.Windows.Threading
Imports System.Windows.Input
Imports System.Windows.Media

Namespace SimpleDigitalClock
    ''' <summary>
    ''' 主窗口类
    ''' </summary>
    Class MainWindow
        Inherits Window

        ' 时钟定时器
        Private WithEvents clockTimer As DispatcherTimer
        
        ' 时间格式标志
        Private is24HourFormat As Boolean = True

        ''' <summary>
        ''' 构造函数
        ''' </summary>
        Public Sub New()
            InitializeComponent()
            InitializeClock()
        End Sub

        ''' <summary>
        ''' 初始化时钟
        ''' </summary>
        Private Sub InitializeClock()
            clockTimer = New DispatcherTimer()
            clockTimer.Interval = TimeSpan.FromSeconds(1)
            clockTimer.Start()
            UpdateTimeDisplay()
        End Sub

        ''' <summary>
        ''' 时钟定时器事件
        ''' </summary>
        Private Sub ClockTimer_Tick(sender As Object, e As EventArgs) Handles clockTimer.Tick
            UpdateTimeDisplay()
        End Sub

        ''' <summary>
        ''' 更新时间显示
        ''' </summary>
        Private Sub UpdateTimeDisplay()
            Dim now As DateTime = DateTime.Now
            
            ' 更新时间
            If is24HourFormat Then
                TimeTextBlock.Text = now.ToString("HH:mm:ss")
                StatusText.Text = "24H"
            Else
                TimeTextBlock.Text = now.ToString("hh:mm:ss tt")
                StatusText.Text = "12H"
            End If
            
            ' 更新日期
            DateTextBlock.Text = now.ToString("yyyy年MM月dd日 dddd")
        End Sub

        ''' <summary>
        ''' 窗口拖动事件
        ''' </summary>
        Private Sub Border_MouseLeftButtonDown(sender As Object, e As MouseButtonEventArgs)
            If e.ButtonState = MouseButtonState.Pressed Then
                Me.DragMove()
            End If
        End Sub

        ''' <summary>
        ''' 置顶菜单点击事件
        ''' </summary>
        Private Sub TopMostMenuItem_Click(sender As Object, e As RoutedEventArgs)
            Me.Topmost = Not Me.Topmost
            TopMostMenuItem.Header = If(Me.Topmost, "取消置顶", "置顶")
        End Sub

        ''' <summary>
        ''' 12小时制菜单点击事件
        ''' </summary>
        Private Sub Hour12MenuItem_Click(sender As Object, e As RoutedEventArgs)
            is24HourFormat = False
            UpdateTimeDisplay()
        End Sub

        ''' <summary>
        ''' 24小时制菜单点击事件
        ''' </summary>
        Private Sub Hour24MenuItem_Click(sender As Object, e As RoutedEventArgs)
            is24HourFormat = True
            UpdateTimeDisplay()
        End Sub

        ''' <summary>
        ''' 关于菜单点击事件
        ''' </summary>
        Private Sub AboutMenuItem_Click(sender As Object, e As RoutedEventArgs)
            MessageBox.Show("极简数字时钟 v1.0" & vbCrLf & "Windows 11 现代化数字时钟应用", "关于", MessageBoxButton.OK, MessageBoxImage.Information)
        End Sub

        ''' <summary>
        ''' 退出菜单点击事件
        ''' </summary>
        Private Sub ExitMenuItem_Click(sender As Object, e As RoutedEventArgs)
            Application.Current.Shutdown()
        End Sub
    End Class
End Namespace
