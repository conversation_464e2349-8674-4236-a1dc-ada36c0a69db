Imports System.IO
Imports System.Text.Json
Imports System.Windows.Media
Imports System.Windows

''' <summary>
''' 设置管理器类
''' </summary>
Public Class SettingsManager
    
    ' 设置文件路径
    Private Shared ReadOnly SettingsFilePath As String = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "DigitalClock", "settings.json")
    
    ' 设置属性
    Public Shared Property FontFamily As String = "Segoe UI"
    Public Shared Property FontSize As Double = 48
    Public Shared Property FontColor As Color = Colors.White
    Public Shared Property WindowOpacity As Double = 0.9
    Public Shared Property IsTopMost As Boolean = False
    Public Shared Property Is24HourFormat As Boolean = True
    Public Shared Property AutoStart As Boolean = False
    Public Shared Property WindowPosition As Point = New Point(100, 100)
    Public Shared Property WindowSize As Size = New Size(400, 200)
    
    ' 预设字体列表
    Public Shared ReadOnly AvailableFonts As String() = {
        "Segoe UI",
        "Consolas", 
        "Arial",
        "Times New Roman",
        "Calibri"
    }
    
    ' 预设颜色列表
    Public Shared ReadOnly AvailableColors As Color() = {
        Colors.White,
        Colors.LightBlue,
        Colors.LightGreen,
        Colors.Yellow,
        Colors.Orange,
        Colors.Pink,
        Colors.LightGray
    }

    ''' <summary>
    ''' 设置数据类
    ''' </summary>
    Private Class SettingsData
        Public Property FontFamily As String
        Public Property FontSize As Double
        Public Property FontColorR As Byte
        Public Property FontColorG As Byte
        Public Property FontColorB As Byte
        Public Property FontColorA As Byte
        Public Property WindowOpacity As Double
        Public Property IsTopMost As Boolean
        Public Property Is24HourFormat As Boolean
        Public Property AutoStart As Boolean
        Public Property WindowPositionX As Double
        Public Property WindowPositionY As Double
        Public Property WindowSizeWidth As Double
        Public Property WindowSizeHeight As Double
    End Class

    ''' <summary>
    ''' 加载设置
    ''' </summary>
    Public Shared Sub LoadSettings()
        Try
            If File.Exists(SettingsFilePath) Then
                Dim jsonString As String = File.ReadAllText(SettingsFilePath)
                Dim data As SettingsData = JsonSerializer.Deserialize(Of SettingsData)(jsonString)
                
                If data IsNot Nothing Then
                    FontFamily = data.FontFamily
                    FontSize = data.FontSize
                    FontColor = Color.FromArgb(data.FontColorA, data.FontColorR, data.FontColorG, data.FontColorB)
                    WindowOpacity = data.WindowOpacity
                    IsTopMost = data.IsTopMost
                    Is24HourFormat = data.Is24HourFormat
                    AutoStart = data.AutoStart
                    WindowPosition = New Point(data.WindowPositionX, data.WindowPositionY)
                    WindowSize = New Size(data.WindowSizeWidth, data.WindowSizeHeight)
                End If
            End If
        Catch ex As Exception
            ' 如果加载失败，使用默认设置
            ResetToDefaults()
        End Try
    End Sub

    ''' <summary>
    ''' 保存设置
    ''' </summary>
    Public Shared Sub SaveSettings()
        Try
            ' 确保目录存在
            Dim directory As String = Path.GetDirectoryName(SettingsFilePath)
            If Not Directory.Exists(directory) Then
                Directory.CreateDirectory(directory)
            End If
            
            ' 创建设置数据对象
            Dim data As New SettingsData With {
                .FontFamily = FontFamily,
                .FontSize = FontSize,
                .FontColorR = FontColor.R,
                .FontColorG = FontColor.G,
                .FontColorB = FontColor.B,
                .FontColorA = FontColor.A,
                .WindowOpacity = WindowOpacity,
                .IsTopMost = IsTopMost,
                .Is24HourFormat = Is24HourFormat,
                .AutoStart = AutoStart,
                .WindowPositionX = WindowPosition.X,
                .WindowPositionY = WindowPosition.Y,
                .WindowSizeWidth = WindowSize.Width,
                .WindowSizeHeight = WindowSize.Height
            }
            
            ' 序列化并保存
            Dim jsonString As String = JsonSerializer.Serialize(data, New JsonSerializerOptions With {.WriteIndented = True})
            File.WriteAllText(SettingsFilePath, jsonString)
            
        Catch ex As Exception
            ' 保存失败时的处理
            System.Diagnostics.Debug.WriteLine($"保存设置失败: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' 重置为默认设置
    ''' </summary>
    Public Shared Sub ResetToDefaults()
        FontFamily = "Segoe UI"
        FontSize = 48
        FontColor = Colors.White
        WindowOpacity = 0.9
        IsTopMost = False
        Is24HourFormat = True
        AutoStart = False
        WindowPosition = New Point(100, 100)
        WindowSize = New Size(400, 200)
    End Sub

    ''' <summary>
    ''' 获取颜色的十六进制字符串
    ''' </summary>
    Public Shared Function GetColorHex(color As Color) As String
        Return $"#{color.A:X2}{color.R:X2}{color.G:X2}{color.B:X2}"
    End Function

    ''' <summary>
    ''' 从十六进制字符串解析颜色
    ''' </summary>
    Public Shared Function ParseColorFromHex(hex As String) As Color
        Try
            Return ColorConverter.ConvertFromString(hex)
        Catch
            Return Colors.White
        End Try
    End Function

    ''' <summary>
    ''' 验证字体是否可用
    ''' </summary>
    Public Shared Function IsFontAvailable(fontName As String) As Boolean
        Try
            Dim font As New FontFamily(fontName)
            Return True
        Catch
            Return False
        End Try
    End Function

    ''' <summary>
    ''' 获取系统可用字体列表
    ''' </summary>
    Public Shared Function GetSystemFonts() As List(Of String)
        Dim fonts As New List(Of String)
        
        For Each font As FontFamily In Fonts.SystemFontFamilies
            fonts.Add(font.Source)
        Next
        
        fonts.Sort()
        Return fonts
    End Function
End Class
