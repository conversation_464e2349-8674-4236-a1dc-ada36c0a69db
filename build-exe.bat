@echo off
echo ========================================
echo    编译极简数字时钟为EXE文件
echo ========================================
echo.

REM 检查是否安装了.NET 6 SDK
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到 .NET 6 SDK
    echo 请从 https://dotnet.microsoft.com/download 下载并安装 .NET 6 SDK
    pause
    exit /b 1
)

echo ✅ .NET 6 SDK 已安装
echo.

if not exist "WorkingClock" (
    echo 错误: WorkingClock 项目不存在
    echo 请先运行 create-simple-clock.bat 创建项目
    pause
    exit /b 1
)

cd WorkingClock

echo 🔧 正在编译项目...
echo.

REM 清理之前的构建
echo 清理之前的构建文件...
dotnet clean --configuration Release

REM 发布为独立可执行文件
echo.
echo 📦 正在发布为独立EXE文件...
echo 这可能需要几分钟时间，请耐心等待...
echo.

REM 发布为单文件可执行程序
dotnet publish --configuration Release --runtime win-x64 --self-contained true --output "../DigitalClockEXE" /p:PublishSingleFile=true /p:IncludeNativeLibrariesForSelfExtract=true /p:PublishTrimmed=false

if %errorlevel% neq 0 (
    echo.
    echo ❌ 编译失败！请检查错误信息。
    cd ..
    pause
    exit /b 1
)

cd ..

echo.
echo ✅ 编译成功！
echo.
echo 📁 输出文件位置: DigitalClockEXE\
echo 📄 主程序文件: DigitalClockEXE\WorkingClock.exe
echo.

if exist "DigitalClockEXE\WorkingClock.exe" (
    echo 📊 文件信息:
    for %%I in ("DigitalClockEXE\WorkingClock.exe") do echo    大小: %%~zI 字节
    echo.
    
    echo 🚀 使用方法:
    echo 1. 直接双击 DigitalClockEXE\WorkingClock.exe 运行
    echo 2. 或者将整个 DigitalClockEXE 文件夹复制到任何地方使用
    echo 3. 无需安装 .NET Runtime，可在任何 Windows 10/11 电脑上运行
    echo.
    
    set /p runNow="是否现在运行编译好的EXE文件？(y/n): "
    if /i "%runNow%"=="y" (
        echo.
        echo 🎉 正在启动极简数字时钟...
        start "" "DigitalClockEXE\WorkingClock.exe"
    )
) else (
    echo ❌ 未找到编译后的EXE文件
)

echo.
pause
