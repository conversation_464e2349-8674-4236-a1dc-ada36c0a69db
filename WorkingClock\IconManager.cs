using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Shapes;

namespace WorkingClock;

/// <summary>
/// 图标管理器 - 提供现代化的矢量图标
/// </summary>
public static class IconManager
{
    /// <summary>
    /// 图标类型枚举
    /// </summary>
    public enum IconType
    {
        Settings,
        Weather,
        Refresh,
        Pin,
        Clock,
        Font,
        Color,
        Size,
        Shadow,
        Border,
        Background,
        Position,
        About,
        Exit,
        Play,
        Pause,
        Stop,
        Minimize,
        Maximize,
        Close,
        Search,
        Download,
        Upload,
        Save,
        Load,
        Copy,
        Paste,
        Cut,
        Undo,
        Redo
    }

    /// <summary>
    /// 获取图标路径数据
    /// </summary>
    public static string GetIconPath(IconType iconType)
    {
        return iconType switch
        {
            IconType.Settings => "M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11.03L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11.03C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z",
            
            IconType.Weather => "M6.5,12A2.5,2.5 0 0,1 9,9.5A2.5,2.5 0 0,1 11.5,12A2.5,2.5 0 0,1 9,14.5A2.5,2.5 0 0,1 6.5,12M12,3L13.09,6.26L16.5,5L15.74,8.5L19,9.59L16.5,11L19,12.41L15.74,13.5L16.5,17L13.09,15.74L12,19L10.91,15.74L7.5,17L8.26,13.5L5,12.41L7.5,11L5,9.59L8.26,8.5L7.5,5L10.91,6.26L12,3Z",
            
            IconType.Refresh => "M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z",
            
            IconType.Pin => "M16,12V4H17V2H7V4H8V12L6,14V16H11.2V22H12.8V16H18V14L16,12Z",
            
            IconType.Clock => "M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M16.2,16.2L11,13V7H12.5V12.2L17,14.7L16.2,16.2Z",
            
            IconType.Font => "M9,4V7H14V19H17V7H22V4H9M3,4V7H8V19H11V7H16V4H3Z",
            
            IconType.Color => "M17.5,12A1.5,1.5 0 0,1 16,10.5A1.5,1.5 0 0,1 17.5,9A1.5,1.5 0 0,1 19,10.5A1.5,1.5 0 0,1 17.5,12M14.5,8A1.5,1.5 0 0,1 13,6.5A1.5,1.5 0 0,1 14.5,5A1.5,1.5 0 0,1 16,6.5A1.5,1.5 0 0,1 14.5,8M9.5,8A1.5,1.5 0 0,1 8,6.5A1.5,1.5 0 0,1 9.5,5A1.5,1.5 0 0,1 11,6.5A1.5,1.5 0 0,1 9.5,8M6.5,12A1.5,1.5 0 0,1 5,10.5A1.5,1.5 0 0,1 6.5,9A1.5,1.5 0 0,1 8,10.5A1.5,1.5 0 0,1 6.5,12M12,3A9,9 0 0,0 3,12A9,9 0 0,0 12,21A1.5,1.5 0 0,0 13.5,19.5C13.5,19.11 13.35,18.76 13.11,18.5C12.88,18.23 12.73,17.88 12.73,17.5A1.5,1.5 0 0,1 14.23,16H16A5,5 0 0,0 21,11C21,6.58 16.97,3 12,3Z",
            
            IconType.Size => "M21,6V8H3V6H21M3,18V16H21V18H3M12,11L16,7H13V3H11V7H8L12,11Z",
            
            IconType.Shadow => "M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20V4Z",
            
            IconType.Border => "M19,19H5V5H19M19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3Z",
            
            IconType.Background => "M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z",
            
            IconType.Position => "M12,11.5A2.5,2.5 0 0,1 9.5,9A2.5,2.5 0 0,1 12,6.5A2.5,2.5 0 0,1 14.5,9A2.5,2.5 0 0,1 12,11.5M12,2A7,7 0 0,0 5,9C5,14.25 12,22 12,22S19,14.25 19,9A7,7 0 0,0 12,2Z",
            
            IconType.About => "M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,17H13V11H11V17Z",
            
            IconType.Exit => "M16,17V14H9V10H16V7L21,12L16,17M14,2A2,2 0 0,1 16,4V6H14V4H5V20H14V18H16V20A2,2 0 0,1 14,22H5A2,2 0 0,1 3,20V4A2,2 0 0,1 5,2H14Z",
            
            IconType.Search => "M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z",
            
            IconType.Close => "M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z",
            
            _ => "M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2Z"
        };
    }

    /// <summary>
    /// 创建图标控件
    /// </summary>
    public static Path CreateIcon(IconType iconType, double size = 16, Brush? fill = null)
    {
        var icon = new Path
        {
            Data = Geometry.Parse(GetIconPath(iconType)),
            Fill = fill ?? new SolidColorBrush(Colors.White),
            Width = size,
            Height = size,
            Stretch = Stretch.Uniform,
            HorizontalAlignment = HorizontalAlignment.Center,
            VerticalAlignment = VerticalAlignment.Center
        };

        return icon;
    }

    /// <summary>
    /// 创建带图标的按钮
    /// </summary>
    public static Button CreateIconButton(IconType iconType, string text = "", double iconSize = 16)
    {
        var button = new Button
        {
            Style = CreateModernButtonStyle(),
            Padding = new Thickness(12, 8, 12, 8),
            Margin = new Thickness(4, 4, 4, 4)
        };

        var stackPanel = new StackPanel
        {
            Orientation = Orientation.Horizontal,
            VerticalAlignment = VerticalAlignment.Center
        };

        // 添加图标
        var icon = CreateIcon(iconType, iconSize);
        stackPanel.Children.Add(icon);

        // 添加文本（如果有）
        if (!string.IsNullOrEmpty(text))
        {
            var textBlock = new TextBlock
            {
                Text = text,
                Margin = new Thickness(8, 0, 0, 0),
                VerticalAlignment = VerticalAlignment.Center
            };
            stackPanel.Children.Add(textBlock);
        }

        button.Content = stackPanel;
        return button;
    }

    /// <summary>
    /// 创建现代化按钮样式
    /// </summary>
    public static Style CreateModernButtonStyle()
    {
        var style = new Style(typeof(Button));
        
        // 设置基本属性
        style.Setters.Add(new Setter(Control.BackgroundProperty, new SolidColorBrush(Color.FromArgb(40, 255, 255, 255))));
        style.Setters.Add(new Setter(Control.ForegroundProperty, new SolidColorBrush(Colors.White)));
        style.Setters.Add(new Setter(Control.BorderBrushProperty, new SolidColorBrush(Color.FromArgb(60, 255, 255, 255))));
        style.Setters.Add(new Setter(Control.BorderThicknessProperty, new Thickness(1)));
        style.Setters.Add(new Setter(Control.PaddingProperty, new Thickness(12, 8, 12, 8)));
        style.Setters.Add(new Setter(Control.MarginProperty, new Thickness(4, 4, 4, 4)));
        style.Setters.Add(new Setter(Control.FontSizeProperty, 12.0));
        style.Setters.Add(new Setter(Control.CursorProperty, System.Windows.Input.Cursors.Hand));

        // 设置模板
        var template = new ControlTemplate(typeof(Button));
        
        var border = new FrameworkElementFactory(typeof(Border));
        border.Name = "border";
        border.SetBinding(Border.BackgroundProperty, new System.Windows.Data.Binding("Background") { RelativeSource = new System.Windows.Data.RelativeSource(System.Windows.Data.RelativeSourceMode.TemplatedParent) });
        border.SetBinding(Border.BorderBrushProperty, new System.Windows.Data.Binding("BorderBrush") { RelativeSource = new System.Windows.Data.RelativeSource(System.Windows.Data.RelativeSourceMode.TemplatedParent) });
        border.SetBinding(Border.BorderThicknessProperty, new System.Windows.Data.Binding("BorderThickness") { RelativeSource = new System.Windows.Data.RelativeSource(System.Windows.Data.RelativeSourceMode.TemplatedParent) });
        border.SetValue(Border.CornerRadiusProperty, new CornerRadius(6));

        var contentPresenter = new FrameworkElementFactory(typeof(ContentPresenter));
        contentPresenter.SetValue(ContentPresenter.HorizontalAlignmentProperty, HorizontalAlignment.Center);
        contentPresenter.SetValue(ContentPresenter.VerticalAlignmentProperty, VerticalAlignment.Center);
        contentPresenter.SetBinding(ContentPresenter.MarginProperty, new System.Windows.Data.Binding("Padding") { RelativeSource = new System.Windows.Data.RelativeSource(System.Windows.Data.RelativeSourceMode.TemplatedParent) });

        border.AppendChild(contentPresenter);
        template.VisualTree = border;

        style.Setters.Add(new Setter(Control.TemplateProperty, template));

        return style;
    }

    /// <summary>
    /// 获取天气图标
    /// </summary>
    public static string GetWeatherIcon(int weatherCode)
    {
        return weatherCode switch
        {
            0 => "☀️", // 晴朗
            1 => "🌤️", // 基本晴朗
            2 => "⛅", // 部分多云
            3 => "☁️", // 阴天
            45 or 48 => "🌫️", // 雾
            51 or 53 or 55 or 61 or 63 or 65 => "🌧️", // 雨
            56 or 57 or 66 or 67 => "🌨️", // 冻雨
            71 or 73 or 75 or 77 => "❄️", // 雪
            80 or 81 or 82 => "🌦️", // 阵雨
            85 or 86 => "🌨️", // 阵雪
            95 or 96 or 99 => "⛈️", // 雷暴
            _ => "🌡️" // 未知
        };
    }

    /// <summary>
    /// 创建现代化的菜单项
    /// </summary>
    public static System.Windows.Controls.MenuItem CreateModernMenuItem(IconType iconType, string header, System.Windows.Input.ICommand? command = null)
    {
        var menuItem = new System.Windows.Controls.MenuItem
        {
            Header = header,
            Command = command
        };

        // 创建图标
        var icon = CreateIcon(iconType, 16, new SolidColorBrush(Colors.White));
        menuItem.Icon = icon;

        return menuItem;
    }
}
