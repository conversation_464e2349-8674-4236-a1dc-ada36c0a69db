<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 时钟文本样式 -->
    <Style x:Key="ClockTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource SegoeUI}"/>
        <Setter Property="FontSize" Value="48"/>
        <Setter Property="FontWeight" Value="Light"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2" BlurRadius="4" Opacity="0.5"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 日期文本样式 -->
    <Style x:Key="DateTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource SegoeUI}"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="FontWeight" Value="Normal"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="Opacity" Value="0.8"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Black" Direction="270" ShadowDepth="1" BlurRadius="2" Opacity="0.3"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 按钮样式 -->
    <Style x:Key="ModernButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="White"/>
        <Setter Property="Padding" Value="10,5"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="FontFamily" Value="{StaticResource SegoeUI}"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="3">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="#33FFFFFF"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="#55FFFFFF"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
