﻿#ExternalChecksum("..\..\..\MainWindow.xaml","{ff1816ec-aa5e-4d10-87f7-6f4963833460}","EEA6F207FDC37624FC25E6663146B8FC76594F2D")
'------------------------------------------------------------------------------
' <auto-generated>
'     此代码由工具生成。
'     运行时版本:4.0.30319.42000
'
'     对此文件的更改可能会导致不正确的行为，并且如果
'     重新生成代码，这些更改将会丢失。
' </auto-generated>
'------------------------------------------------------------------------------

Option Strict Off
Option Explicit On

Imports System
Imports System.Diagnostics
Imports System.Windows
Imports System.Windows.Automation
Imports System.Windows.Controls
Imports System.Windows.Controls.Primitives
Imports System.Windows.Controls.Ribbon
Imports System.Windows.Data
Imports System.Windows.Documents
Imports System.Windows.Forms.Integration
Imports System.Windows.Ink
Imports System.Windows.Input
Imports System.Windows.Markup
Imports System.Windows.Media
Imports System.Windows.Media.Animation
Imports System.Windows.Media.Effects
Imports System.Windows.Media.Imaging
Imports System.Windows.Media.Media3D
Imports System.Windows.Media.TextFormatting
Imports System.Windows.Navigation
Imports System.Windows.Shapes
Imports System.Windows.Shell

Namespace DigitalClock
    
    '''<summary>
    '''MainWindow
    '''</summary>
    <Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>  _
    Partial Public Class MainWindow
        Inherits System.Windows.Window
        Implements System.Windows.Markup.IComponentConnector
        
        
        #ExternalSource("..\..\..\MainWindow.xaml",16)
        <System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")>  _
        Friend WithEvents MainBorder As System.Windows.Controls.Border
        
        #End ExternalSource
        
        
        #ExternalSource("..\..\..\MainWindow.xaml",42)
        <System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")>  _
        Friend WithEvents TimeTextBlock As System.Windows.Controls.TextBlock
        
        #End ExternalSource
        
        
        #ExternalSource("..\..\..\MainWindow.xaml",49)
        <System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")>  _
        Friend WithEvents DateTextBlock As System.Windows.Controls.TextBlock
        
        #End ExternalSource
        
        
        #ExternalSource("..\..\..\MainWindow.xaml",61)
        <System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")>  _
        Friend WithEvents ControlPanel As System.Windows.Controls.StackPanel
        
        #End ExternalSource
        
        
        #ExternalSource("..\..\..\MainWindow.xaml",64)
        <System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")>  _
        Friend WithEvents SettingsButton As System.Windows.Controls.Button
        
        #End ExternalSource
        
        
        #ExternalSource("..\..\..\MainWindow.xaml",69)
        <System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")>  _
        Friend WithEvents TopMostButton As System.Windows.Controls.Button
        
        #End ExternalSource
        
        
        #ExternalSource("..\..\..\MainWindow.xaml",74)
        <System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")>  _
        Friend WithEvents MinimizeButton As System.Windows.Controls.Button
        
        #End ExternalSource
        
        
        #ExternalSource("..\..\..\MainWindow.xaml",79)
        <System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")>  _
        Friend WithEvents CloseButton As System.Windows.Controls.Button
        
        #End ExternalSource
        
        
        #ExternalSource("..\..\..\MainWindow.xaml",91)
        <System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")>  _
        Friend WithEvents TopMostIndicator As System.Windows.Shapes.Ellipse
        
        #End ExternalSource
        
        
        #ExternalSource("..\..\..\MainWindow.xaml",98)
        <System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")>  _
        Friend WithEvents StatusText As System.Windows.Controls.TextBlock
        
        #End ExternalSource
        
        
        #ExternalSource("..\..\..\MainWindow.xaml",133)
        <System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")>  _
        Friend WithEvents TopMostMenuItem As System.Windows.Controls.MenuItem
        
        #End ExternalSource
        
        
        #ExternalSource("..\..\..\MainWindow.xaml",135)
        <System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")>  _
        Friend WithEvents Hour12MenuItem As System.Windows.Controls.MenuItem
        
        #End ExternalSource
        
        
        #ExternalSource("..\..\..\MainWindow.xaml",136)
        <System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")>  _
        Friend WithEvents Hour24MenuItem As System.Windows.Controls.MenuItem
        
        #End ExternalSource
        
        
        #ExternalSource("..\..\..\MainWindow.xaml",138)
        <System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")>  _
        Friend WithEvents AutoStartMenuItem As System.Windows.Controls.MenuItem
        
        #End ExternalSource
        
        Private _contentLoaded As Boolean
        
        '''<summary>
        '''InitializeComponent
        '''</summary>
        <System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")>  _
        Public Sub InitializeComponent() Implements System.Windows.Markup.IComponentConnector.InitializeComponent
            If _contentLoaded Then
                Return
            End If
            _contentLoaded = true
            Dim resourceLocater As System.Uri = New System.Uri("/SimpleDigitalClock;V1.0.0.0;component/mainwindow.xaml", System.UriKind.Relative)
            
            #ExternalSource("..\..\..\MainWindow.xaml",1)
            System.Windows.Application.LoadComponent(Me, resourceLocater)
            
            #End ExternalSource
        End Sub
        
        <System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0"),  _
         System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never),  _
         System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes"),  _
         System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity"),  _
         System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")>  _
        Sub System_Windows_Markup_IComponentConnector_Connect(ByVal connectionId As Integer, ByVal target As Object) Implements System.Windows.Markup.IComponentConnector.Connect
            If (connectionId = 1) Then
                Me.MainBorder = CType(target,System.Windows.Controls.Border)
                
                #ExternalSource("..\..\..\MainWindow.xaml",21)
                AddHandler Me.MainBorder.MouseLeftButtonDown, New System.Windows.Input.MouseButtonEventHandler(AddressOf Me.Border_MouseLeftButtonDown)
                
                #End ExternalSource
                Return
            End If
            If (connectionId = 2) Then
                Me.TimeTextBlock = CType(target,System.Windows.Controls.TextBlock)
                Return
            End If
            If (connectionId = 3) Then
                Me.DateTextBlock = CType(target,System.Windows.Controls.TextBlock)
                Return
            End If
            If (connectionId = 4) Then
                Me.ControlPanel = CType(target,System.Windows.Controls.StackPanel)
                Return
            End If
            If (connectionId = 5) Then
                Me.SettingsButton = CType(target,System.Windows.Controls.Button)
                
                #ExternalSource("..\..\..\MainWindow.xaml",67)
                AddHandler Me.SettingsButton.Click, New System.Windows.RoutedEventHandler(AddressOf Me.SettingsButton_Click)
                
                #End ExternalSource
                Return
            End If
            If (connectionId = 6) Then
                Me.TopMostButton = CType(target,System.Windows.Controls.Button)
                
                #ExternalSource("..\..\..\MainWindow.xaml",72)
                AddHandler Me.TopMostButton.Click, New System.Windows.RoutedEventHandler(AddressOf Me.TopMostButton_Click)
                
                #End ExternalSource
                Return
            End If
            If (connectionId = 7) Then
                Me.MinimizeButton = CType(target,System.Windows.Controls.Button)
                
                #ExternalSource("..\..\..\MainWindow.xaml",77)
                AddHandler Me.MinimizeButton.Click, New System.Windows.RoutedEventHandler(AddressOf Me.MinimizeButton_Click)
                
                #End ExternalSource
                Return
            End If
            If (connectionId = 8) Then
                Me.CloseButton = CType(target,System.Windows.Controls.Button)
                
                #ExternalSource("..\..\..\MainWindow.xaml",82)
                AddHandler Me.CloseButton.Click, New System.Windows.RoutedEventHandler(AddressOf Me.CloseButton_Click)
                
                #End ExternalSource
                Return
            End If
            If (connectionId = 9) Then
                Me.TopMostIndicator = CType(target,System.Windows.Shapes.Ellipse)
                Return
            End If
            If (connectionId = 10) Then
                Me.StatusText = CType(target,System.Windows.Controls.TextBlock)
                Return
            End If
            If (connectionId = 11) Then
                
                #ExternalSource("..\..\..\MainWindow.xaml",132)
                AddHandler CType(target,System.Windows.Controls.MenuItem).Click, New System.Windows.RoutedEventHandler(AddressOf Me.SettingsMenuItem_Click)
                
                #End ExternalSource
                Return
            End If
            If (connectionId = 12) Then
                Me.TopMostMenuItem = CType(target,System.Windows.Controls.MenuItem)
                
                #ExternalSource("..\..\..\MainWindow.xaml",133)
                AddHandler Me.TopMostMenuItem.Click, New System.Windows.RoutedEventHandler(AddressOf Me.TopMostMenuItem_Click)
                
                #End ExternalSource
                Return
            End If
            If (connectionId = 13) Then
                Me.Hour12MenuItem = CType(target,System.Windows.Controls.MenuItem)
                
                #ExternalSource("..\..\..\MainWindow.xaml",135)
                AddHandler Me.Hour12MenuItem.Click, New System.Windows.RoutedEventHandler(AddressOf Me.Hour12MenuItem_Click)
                
                #End ExternalSource
                Return
            End If
            If (connectionId = 14) Then
                Me.Hour24MenuItem = CType(target,System.Windows.Controls.MenuItem)
                
                #ExternalSource("..\..\..\MainWindow.xaml",136)
                AddHandler Me.Hour24MenuItem.Click, New System.Windows.RoutedEventHandler(AddressOf Me.Hour24MenuItem_Click)
                
                #End ExternalSource
                Return
            End If
            If (connectionId = 15) Then
                Me.AutoStartMenuItem = CType(target,System.Windows.Controls.MenuItem)
                
                #ExternalSource("..\..\..\MainWindow.xaml",138)
                AddHandler Me.AutoStartMenuItem.Click, New System.Windows.RoutedEventHandler(AddressOf Me.AutoStartMenuItem_Click)
                
                #End ExternalSource
                Return
            End If
            If (connectionId = 16) Then
                
                #ExternalSource("..\..\..\MainWindow.xaml",140)
                AddHandler CType(target,System.Windows.Controls.MenuItem).Click, New System.Windows.RoutedEventHandler(AddressOf Me.AboutMenuItem_Click)
                
                #End ExternalSource
                Return
            End If
            If (connectionId = 17) Then
                
                #ExternalSource("..\..\..\MainWindow.xaml",141)
                AddHandler CType(target,System.Windows.Controls.MenuItem).Click, New System.Windows.RoutedEventHandler(AddressOf Me.ExitMenuItem_Click)
                
                #End ExternalSource
                Return
            End If
            Me._contentLoaded = true
        End Sub
    End Class
End Namespace

