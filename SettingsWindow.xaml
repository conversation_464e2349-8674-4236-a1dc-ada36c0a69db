<Window x:Class="DigitalClock.SettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="设置" 
        Height="500" 
        Width="400"
        WindowStyle="SingleBorderWindow"
        ResizeMode="NoResize"
        WindowStartupLocation="CenterOwner"
        Background="#F3F2F1">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" 
                   Text="时钟设置" 
                   FontSize="24" 
                   FontWeight="Bold" 
                   Foreground="#323130"
                   Margin="0,0,0,20"/>

        <!-- 设置内容 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                
                <!-- 字体设置 -->
                <GroupBox Header="字体设置" Margin="0,0,0,15">
                    <StackPanel Margin="10">
                        
                        <!-- 字体族 -->
                        <Label Content="字体:" FontWeight="Bold"/>
                        <ComboBox x:Name="FontFamilyComboBox" 
                                 Margin="0,5,0,10"
                                 SelectionChanged="FontFamilyComboBox_SelectionChanged"/>
                        
                        <!-- 字体大小 -->
                        <Label Content="大小:" FontWeight="Bold"/>
                        <StackPanel Orientation="Horizontal" Margin="0,5,0,10">
                            <Slider x:Name="FontSizeSlider" 
                                   Width="200" 
                                   Minimum="20" 
                                   Maximum="100" 
                                   Value="48"
                                   ValueChanged="FontSizeSlider_ValueChanged"/>
                            <TextBlock x:Name="FontSizeText" 
                                      Text="48" 
                                      VerticalAlignment="Center" 
                                      Margin="10,0,0,0"/>
                        </StackPanel>
                        
                        <!-- 字体颜色 -->
                        <Label Content="颜色:" FontWeight="Bold"/>
                        <StackPanel Orientation="Horizontal" Margin="0,5,0,10">
                            <ComboBox x:Name="FontColorComboBox" 
                                     Width="150"
                                     SelectionChanged="FontColorComboBox_SelectionChanged"/>
                            <Rectangle x:Name="ColorPreview" 
                                      Width="30" 
                                      Height="20" 
                                      Margin="10,0,0,0" 
                                      Stroke="Gray" 
                                      StrokeThickness="1"/>
                        </StackPanel>
                    </StackPanel>
                </GroupBox>

                <!-- 窗口设置 -->
                <GroupBox Header="窗口设置" Margin="0,0,0,15">
                    <StackPanel Margin="10">
                        
                        <!-- 透明度 -->
                        <Label Content="透明度:" FontWeight="Bold"/>
                        <StackPanel Orientation="Horizontal" Margin="0,5,0,10">
                            <Slider x:Name="OpacitySlider" 
                                   Width="200" 
                                   Minimum="0.3" 
                                   Maximum="1.0" 
                                   Value="0.9"
                                   ValueChanged="OpacitySlider_ValueChanged"/>
                            <TextBlock x:Name="OpacityText" 
                                      Text="90%" 
                                      VerticalAlignment="Center" 
                                      Margin="10,0,0,0"/>
                        </StackPanel>
                        
                        <!-- 窗口选项 -->
                        <CheckBox x:Name="TopMostCheckBox" 
                                 Content="窗口置顶" 
                                 Margin="0,5"
                                 Checked="TopMostCheckBox_Checked"
                                 Unchecked="TopMostCheckBox_Unchecked"/>
                        
                        <CheckBox x:Name="AutoStartCheckBox" 
                                 Content="开机自启" 
                                 Margin="0,5"
                                 Checked="AutoStartCheckBox_Checked"
                                 Unchecked="AutoStartCheckBox_Unchecked"/>
                    </StackPanel>
                </GroupBox>

                <!-- 时间格式设置 -->
                <GroupBox Header="时间格式" Margin="0,0,0,15">
                    <StackPanel Margin="10">
                        <RadioButton x:Name="Hour12RadioButton" 
                                    Content="12小时制 (上午/下午)" 
                                    GroupName="TimeFormat"
                                    Margin="0,5"
                                    Checked="Hour12RadioButton_Checked"/>
                        
                        <RadioButton x:Name="Hour24RadioButton" 
                                    Content="24小时制" 
                                    GroupName="TimeFormat"
                                    Margin="0,5"
                                    Checked="Hour24RadioButton_Checked"/>
                    </StackPanel>
                </GroupBox>

                <!-- 预览区域 -->
                <GroupBox Header="预览" Margin="0,0,0,15">
                    <Border Background="Black" 
                           Height="80" 
                           Margin="10"
                           CornerRadius="5">
                        <TextBlock x:Name="PreviewTextBlock" 
                                  Text="12:34:56" 
                                  HorizontalAlignment="Center" 
                                  VerticalAlignment="Center"
                                  Foreground="White"
                                  FontSize="24"/>
                    </Border>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>

        <!-- 按钮区域 -->
        <StackPanel Grid.Row="2" 
                   Orientation="Horizontal" 
                   HorizontalAlignment="Right" 
                   Margin="0,20,0,0">
            
            <Button x:Name="ResetButton" 
                   Content="重置默认" 
                   Width="80" 
                   Height="30" 
                   Margin="0,0,10,0"
                   Click="ResetButton_Click"/>
            
            <Button x:Name="ApplyButton" 
                   Content="应用" 
                   Width="60" 
                   Height="30" 
                   Margin="0,0,10,0"
                   Click="ApplyButton_Click"/>
            
            <Button x:Name="OkButton" 
                   Content="确定" 
                   Width="60" 
                   Height="30" 
                   Margin="0,0,10,0"
                   Click="OkButton_Click"/>
            
            <Button x:Name="CancelButton" 
                   Content="取消" 
                   Width="60" 
                   Height="30"
                   Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
