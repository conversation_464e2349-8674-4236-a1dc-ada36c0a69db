<Window x:Class="WorkingClock.SettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="时钟设置" 
        Height="600" 
        Width="450"
        WindowStyle="SingleBorderWindow"
        ResizeMode="NoResize"
        WindowStartupLocation="CenterOwner"
        Background="#F5F5F5">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" 
                   Text="⚙️ 时钟个性化设置" 
                   FontSize="24" 
                   FontWeight="Bold" 
                   Foreground="#2C3E50"
                   HorizontalAlignment="Center"
                   Margin="0,0,0,20"/>

        <!-- 设置内容 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                
                <!-- 外观设计 -->
                <GroupBox Header="🎨 外观设计" Margin="0,0,0,15" Padding="15">
                    <StackPanel>
                        
                        <!-- 边框样式 -->
                        <Label Content="边框样式:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="BorderStyleComboBox" Margin="0,0,0,15" SelectionChanged="BorderStyleComboBox_SelectionChanged">
                            <ComboBoxItem Content="圆角边框 (现代)" Tag="10"/>
                            <ComboBoxItem Content="小圆角边框" Tag="5"/>
                            <ComboBoxItem Content="直角边框 (经典)" Tag="0"/>
                            <ComboBoxItem Content="大圆角边框 (柔和)" Tag="20"/>
                        </ComboBox>
                        
                        <!-- 边框颜色 -->
                        <Label Content="边框颜色:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="BorderColorComboBox" Margin="0,0,0,15" SelectionChanged="BorderColorComboBox_SelectionChanged">
                            <ComboBoxItem Content="白色 (默认)" Tag="#40FFFFFF"/>
                            <ComboBoxItem Content="蓝色" Tag="#4000BFFF"/>
                            <ComboBoxItem Content="绿色" Tag="#4000FF00"/>
                            <ComboBoxItem Content="金色" Tag="#40FFD700"/>
                            <ComboBoxItem Content="紫色" Tag="#40800080"/>
                            <ComboBoxItem Content="红色" Tag="#40FF0000"/>
                            <ComboBoxItem Content="无边框" Tag="Transparent"/>
                        </ComboBox>
                        
                        <!-- 背景透明度 -->
                        <Label Content="背景透明度:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                            <Slider x:Name="BackgroundOpacitySlider" 
                                   Width="250" 
                                   Minimum="0.1" 
                                   Maximum="1.0" 
                                   Value="0.5"
                                   ValueChanged="BackgroundOpacitySlider_ValueChanged"/>
                            <TextBlock x:Name="BackgroundOpacityText" 
                                      Text="50%" 
                                      VerticalAlignment="Center" 
                                      Margin="10,0,0,0"
                                      MinWidth="40"/>
                        </StackPanel>
                        
                        <!-- 背景颜色 -->
                        <Label Content="背景颜色:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="BackgroundColorComboBox" Margin="0,0,0,10" SelectionChanged="BackgroundColorComboBox_SelectionChanged">
                            <ComboBoxItem Content="黑色 (默认)" Tag="Black"/>
                            <ComboBoxItem Content="深蓝色" Tag="#001122"/>
                            <ComboBoxItem Content="深绿色" Tag="#001100"/>
                            <ComboBoxItem Content="深紫色" Tag="#220022"/>
                            <ComboBoxItem Content="深红色" Tag="#220000"/>
                            <ComboBoxItem Content="深灰色" Tag="#333333"/>
                        </ComboBox>
                    </StackPanel>
                </GroupBox>

                <!-- 字体设置 -->
                <GroupBox Header="🔤 字体设置" Margin="0,0,0,15" Padding="15">
                    <StackPanel>
                        
                        <!-- 字体族 -->
                        <Label Content="字体:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="FontFamilyComboBox" Margin="0,0,0,15" SelectionChanged="FontFamilyComboBox_SelectionChanged">
                            <ComboBoxItem Content="DS-Digital (LED数字)" Tag="DS-Digital"/>
                            <ComboBoxItem Content="Segoe UI (默认)" Tag="Segoe UI"/>
                            <ComboBoxItem Content="Arial" Tag="Arial"/>
                            <ComboBoxItem Content="Consolas (等宽)" Tag="Consolas"/>
                            <ComboBoxItem Content="Times New Roman" Tag="Times New Roman"/>
                            <ComboBoxItem Content="Calibri" Tag="Calibri"/>
                            <ComboBoxItem Content="Microsoft YaHei (微软雅黑)" Tag="Microsoft YaHei"/>
                            <ComboBoxItem Content="SimSun (宋体)" Tag="SimSun"/>
                            <ComboBoxItem Content="Courier New (打字机)" Tag="Courier New"/>
                        </ComboBox>
                        
                        <!-- 字体大小 -->
                        <Label Content="字体大小:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                            <Slider x:Name="FontSizeSlider" 
                                   Width="250" 
                                   Minimum="20" 
                                   Maximum="100" 
                                   Value="48"
                                   ValueChanged="FontSizeSlider_ValueChanged"/>
                            <TextBlock x:Name="FontSizeText" 
                                      Text="48px" 
                                      VerticalAlignment="Center" 
                                      Margin="10,0,0,0"
                                      MinWidth="50"/>
                        </StackPanel>
                        
                        <!-- 字体颜色 -->
                        <Label Content="字体颜色:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="FontColorComboBox" Margin="0,0,0,15" SelectionChanged="FontColorComboBox_SelectionChanged">
                            <ComboBoxItem Content="LED绿色 (推荐)" Tag="LimeGreen"/>
                            <ComboBoxItem Content="LED红色" Tag="Red"/>
                            <ComboBoxItem Content="LED蓝色" Tag="DeepSkyBlue"/>
                            <ComboBoxItem Content="LED橙色" Tag="OrangeRed"/>
                            <ComboBoxItem Content="白色 (默认)" Tag="White"/>
                            <ComboBoxItem Content="浅蓝色" Tag="LightBlue"/>
                            <ComboBoxItem Content="浅绿色" Tag="LightGreen"/>
                            <ComboBoxItem Content="黄色" Tag="Yellow"/>
                            <ComboBoxItem Content="橙色" Tag="Orange"/>
                            <ComboBoxItem Content="粉色" Tag="Pink"/>
                            <ComboBoxItem Content="浅灰色" Tag="LightGray"/>
                            <ComboBoxItem Content="金色" Tag="Gold"/>
                        </ComboBox>
                        
                        <!-- 字体粗细 -->
                        <Label Content="字体粗细:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="FontWeightComboBox" Margin="0,0,0,10" SelectionChanged="FontWeightComboBox_SelectionChanged">
                            <ComboBoxItem Content="细体 (默认)" Tag="Light"/>
                            <ComboBoxItem Content="正常" Tag="Normal"/>
                            <ComboBoxItem Content="粗体" Tag="Bold"/>
                            <ComboBoxItem Content="特粗" Tag="Black"/>
                        </ComboBox>
                    </StackPanel>
                </GroupBox>

                <!-- 阴影效果 -->
                <GroupBox Header="🌟 阴影效果" Margin="0,0,0,15" Padding="15">
                    <StackPanel>
                        
                        <!-- 阴影强度 -->
                        <Label Content="阴影强度:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                            <Slider x:Name="ShadowOpacitySlider" 
                                   Width="250" 
                                   Minimum="0" 
                                   Maximum="1.0" 
                                   Value="0.5"
                                   ValueChanged="ShadowOpacitySlider_ValueChanged"/>
                            <TextBlock x:Name="ShadowOpacityText" 
                                      Text="50%" 
                                      VerticalAlignment="Center" 
                                      Margin="10,0,0,0"
                                      MinWidth="40"/>
                        </StackPanel>
                        
                        <!-- 阴影模糊 -->
                        <Label Content="阴影模糊:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                            <Slider x:Name="ShadowBlurSlider" 
                                   Width="250" 
                                   Minimum="0" 
                                   Maximum="20" 
                                   Value="4"
                                   ValueChanged="ShadowBlurSlider_ValueChanged"/>
                            <TextBlock x:Name="ShadowBlurText" 
                                      Text="4px" 
                                      VerticalAlignment="Center" 
                                      Margin="10,0,0,0"
                                      MinWidth="40"/>
                        </StackPanel>
                    </StackPanel>
                </GroupBox>

                <!-- 窗口设置 -->
                <GroupBox Header="🪟 窗口设置" Margin="0,0,0,15" Padding="15">
                    <StackPanel>
                        
                        <!-- 窗口大小 -->
                        <Label Content="窗口大小:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="WindowSizeComboBox" Margin="0,0,0,15" SelectionChanged="WindowSizeComboBox_SelectionChanged">
                            <ComboBoxItem Content="小 (300x150)" Tag="300,150"/>
                            <ComboBoxItem Content="中 (400x200)" Tag="400,200"/>
                            <ComboBoxItem Content="大 (500x250)" Tag="500,250"/>
                            <ComboBoxItem Content="特大 (600x300)" Tag="600,300"/>
                        </ComboBox>
                        
                        <!-- 窗口位置 -->
                        <Label Content="窗口位置:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="WindowPositionComboBox" Margin="0,0,0,10" SelectionChanged="WindowPositionComboBox_SelectionChanged">
                            <ComboBoxItem Content="屏幕中央 (默认)" Tag="Center"/>
                            <ComboBoxItem Content="左上角" Tag="TopLeft"/>
                            <ComboBoxItem Content="右上角" Tag="TopRight"/>
                            <ComboBoxItem Content="左下角" Tag="BottomLeft"/>
                            <ComboBoxItem Content="右下角" Tag="BottomRight"/>
                        </ComboBox>
                    </StackPanel>
                </GroupBox>

                <!-- 预览区域 -->
                <GroupBox Header="👀 实时预览" Margin="0,0,0,15" Padding="15">
                    <Border x:Name="PreviewBorder" 
                           Height="80" 
                           Background="#80000000"
                           CornerRadius="10"
                           BorderThickness="1"
                           BorderBrush="#40FFFFFF">
                        <TextBlock x:Name="PreviewTextBlock" 
                                  Text="12:34:56" 
                                  HorizontalAlignment="Center" 
                                  VerticalAlignment="Center"
                                  Foreground="White"
                                  FontFamily="Segoe UI"
                                  FontSize="24"
                                  FontWeight="Light">
                            <TextBlock.Effect>
                                <DropShadowEffect x:Name="PreviewShadow" 
                                                 Color="Black" 
                                                 Direction="270" 
                                                 ShadowDepth="2" 
                                                 BlurRadius="4" 
                                                 Opacity="0.5"/>
                            </TextBlock.Effect>
                        </TextBlock>
                    </Border>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>

        <!-- 按钮区域 -->
        <StackPanel Grid.Row="2" 
                   Orientation="Horizontal" 
                   HorizontalAlignment="Right" 
                   Margin="0,20,0,0">
            
            <Button x:Name="ResetButton" 
                   Content="🔄 重置默认" 
                   Width="100" 
                   Height="35" 
                   Margin="0,0,10,0"
                   Click="ResetButton_Click"/>
            
            <Button x:Name="ApplyButton" 
                   Content="✅ 应用" 
                   Width="80" 
                   Height="35" 
                   Margin="0,0,10,0"
                   Click="ApplyButton_Click"/>
            
            <Button x:Name="OkButton" 
                   Content="确定" 
                   Width="80" 
                   Height="35" 
                   Margin="0,0,10,0"
                   Click="OkButton_Click"/>
            
            <Button x:Name="CancelButton" 
                   Content="取消" 
                   Width="80" 
                   Height="35"
                   Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
