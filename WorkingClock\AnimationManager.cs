using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;

namespace WorkingClock;

/// <summary>
/// 动画管理器 - 处理各种UI动画效果
/// </summary>
public static class AnimationManager
{
    /// <summary>
    /// 动画类型枚举
    /// </summary>
    public enum AnimationType
    {
        FlipPage,      // 翻页效果
        Slide,         // 滑动效果
        Fade,          // 淡入淡出
        Scale,         // 缩放效果
        Bounce,        // 弹跳效果
        Glow,          // 发光效果
        Typewriter     // 打字机效果
    }

    /// <summary>
    /// 翻页动画效果
    /// </summary>
    public static void ApplyFlipPageAnimation(FrameworkElement element, string newText, Action<string> updateText)
    {
        var storyboard = new Storyboard();
        
        // 第一阶段：向后翻转
        var rotateTransform = new RotateTransform();
        element.RenderTransform = rotateTransform;
        element.RenderTransformOrigin = new Point(0.5, 0.5);
        
        var rotation1 = new DoubleAnimation
        {
            From = 0,
            To = 90,
            Duration = TimeSpan.FromMilliseconds(150),
            EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseIn }
        };
        
        Storyboard.SetTarget(rotation1, element);
        Storyboard.SetTargetProperty(rotation1, new PropertyPath("RenderTransform.Angle"));
        storyboard.Children.Add(rotation1);
        
        // 中间更新文本
        storyboard.Completed += (s, e) =>
        {
            updateText(newText);
            
            // 第二阶段：向前翻转
            var storyboard2 = new Storyboard();
            var rotation2 = new DoubleAnimation
            {
                From = -90,
                To = 0,
                Duration = TimeSpan.FromMilliseconds(150),
                EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseOut }
            };
            
            Storyboard.SetTarget(rotation2, element);
            Storyboard.SetTargetProperty(rotation2, new PropertyPath("RenderTransform.Angle"));
            storyboard2.Children.Add(rotation2);
            storyboard2.Begin();
        };
        
        storyboard.Begin();
    }

    /// <summary>
    /// 滑动动画效果
    /// </summary>
    public static void ApplySlideAnimation(FrameworkElement element, string newText, Action<string> updateText)
    {
        var storyboard = new Storyboard();
        
        var translateTransform = new TranslateTransform();
        element.RenderTransform = translateTransform;
        
        // 向上滑出
        var slideOut = new DoubleAnimation
        {
            From = 0,
            To = -element.ActualHeight,
            Duration = TimeSpan.FromMilliseconds(200),
            EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseIn }
        };
        
        Storyboard.SetTarget(slideOut, element);
        Storyboard.SetTargetProperty(slideOut, new PropertyPath("RenderTransform.Y"));
        storyboard.Children.Add(slideOut);
        
        storyboard.Completed += (s, e) =>
        {
            updateText(newText);
            
            // 从下方滑入
            var storyboard2 = new Storyboard();
            translateTransform.Y = element.ActualHeight;
            
            var slideIn = new DoubleAnimation
            {
                From = element.ActualHeight,
                To = 0,
                Duration = TimeSpan.FromMilliseconds(200),
                EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseOut }
            };
            
            Storyboard.SetTarget(slideIn, element);
            Storyboard.SetTargetProperty(slideIn, new PropertyPath("RenderTransform.Y"));
            storyboard2.Children.Add(slideIn);
            storyboard2.Begin();
        };
        
        storyboard.Begin();
    }

    /// <summary>
    /// 淡入淡出动画
    /// </summary>
    public static void ApplyFadeAnimation(FrameworkElement element, string newText, Action<string> updateText)
    {
        var storyboard = new Storyboard();
        
        var fadeOut = new DoubleAnimation
        {
            From = 1.0,
            To = 0.0,
            Duration = TimeSpan.FromMilliseconds(150)
        };
        
        Storyboard.SetTarget(fadeOut, element);
        Storyboard.SetTargetProperty(fadeOut, new PropertyPath("Opacity"));
        storyboard.Children.Add(fadeOut);
        
        storyboard.Completed += (s, e) =>
        {
            updateText(newText);
            
            var storyboard2 = new Storyboard();
            var fadeIn = new DoubleAnimation
            {
                From = 0.0,
                To = 1.0,
                Duration = TimeSpan.FromMilliseconds(150)
            };
            
            Storyboard.SetTarget(fadeIn, element);
            Storyboard.SetTargetProperty(fadeIn, new PropertyPath("Opacity"));
            storyboard2.Children.Add(fadeIn);
            storyboard2.Begin();
        };
        
        storyboard.Begin();
    }

    /// <summary>
    /// 缩放动画效果
    /// </summary>
    public static void ApplyScaleAnimation(FrameworkElement element, string newText, Action<string> updateText)
    {
        var storyboard = new Storyboard();
        
        var scaleTransform = new ScaleTransform();
        element.RenderTransform = scaleTransform;
        element.RenderTransformOrigin = new Point(0.5, 0.5);
        
        var scaleDown = new DoubleAnimation
        {
            From = 1.0,
            To = 0.0,
            Duration = TimeSpan.FromMilliseconds(150),
            EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseIn }
        };
        
        Storyboard.SetTarget(scaleDown, element);
        Storyboard.SetTargetProperty(scaleDown, new PropertyPath("RenderTransform.ScaleX"));
        storyboard.Children.Add(scaleDown);
        
        var scaleDownY = scaleDown.Clone();
        Storyboard.SetTargetProperty(scaleDownY, new PropertyPath("RenderTransform.ScaleY"));
        storyboard.Children.Add(scaleDownY);
        
        storyboard.Completed += (s, e) =>
        {
            updateText(newText);
            
            var storyboard2 = new Storyboard();
            var scaleUp = new DoubleAnimation
            {
                From = 0.0,
                To = 1.0,
                Duration = TimeSpan.FromMilliseconds(150),
                EasingFunction = new BackEase { EasingMode = EasingMode.EaseOut, Amplitude = 0.3 }
            };
            
            Storyboard.SetTarget(scaleUp, element);
            Storyboard.SetTargetProperty(scaleUp, new PropertyPath("RenderTransform.ScaleX"));
            storyboard2.Children.Add(scaleUp);
            
            var scaleUpY = scaleUp.Clone();
            Storyboard.SetTargetProperty(scaleUpY, new PropertyPath("RenderTransform.ScaleY"));
            storyboard2.Children.Add(scaleUpY);
            
            storyboard2.Begin();
        };
        
        storyboard.Begin();
    }

    /// <summary>
    /// 发光动画效果
    /// </summary>
    public static void ApplyGlowAnimation(FrameworkElement element)
    {
        var glowEffect = new DropShadowEffect
        {
            Color = Colors.White,
            BlurRadius = 20,
            ShadowDepth = 0,
            Opacity = 0
        };
        
        element.Effect = glowEffect;
        
        var storyboard = new Storyboard();
        storyboard.RepeatBehavior = RepeatBehavior.Forever;
        storyboard.AutoReverse = true;
        
        var glowAnimation = new DoubleAnimation
        {
            From = 0,
            To = 0.8,
            Duration = TimeSpan.FromSeconds(2),
            EasingFunction = new SineEase { EasingMode = EasingMode.EaseInOut }
        };
        
        Storyboard.SetTarget(glowAnimation, element);
        Storyboard.SetTargetProperty(glowAnimation, new PropertyPath("Effect.Opacity"));
        storyboard.Children.Add(glowAnimation);
        
        storyboard.Begin();
    }

    /// <summary>
    /// 弹跳进入动画
    /// </summary>
    public static void ApplyBounceInAnimation(FrameworkElement element)
    {
        var scaleTransform = new ScaleTransform(0, 0);
        element.RenderTransform = scaleTransform;
        element.RenderTransformOrigin = new Point(0.5, 0.5);
        
        var storyboard = new Storyboard();
        
        var scaleAnimation = new DoubleAnimation
        {
            From = 0,
            To = 1,
            Duration = TimeSpan.FromMilliseconds(600),
            EasingFunction = new BounceEase { EasingMode = EasingMode.EaseOut, Bounces = 2, Bounciness = 2 }
        };
        
        Storyboard.SetTarget(scaleAnimation, element);
        Storyboard.SetTargetProperty(scaleAnimation, new PropertyPath("RenderTransform.ScaleX"));
        storyboard.Children.Add(scaleAnimation);
        
        var scaleAnimationY = scaleAnimation.Clone();
        Storyboard.SetTargetProperty(scaleAnimationY, new PropertyPath("RenderTransform.ScaleY"));
        storyboard.Children.Add(scaleAnimationY);
        
        storyboard.Begin();
    }

    /// <summary>
    /// 应用指定类型的动画
    /// </summary>
    public static void ApplyAnimation(AnimationType type, FrameworkElement element, string newText, Action<string> updateText)
    {
        switch (type)
        {
            case AnimationType.FlipPage:
                ApplyFlipPageAnimation(element, newText, updateText);
                break;
            case AnimationType.Slide:
                ApplySlideAnimation(element, newText, updateText);
                break;
            case AnimationType.Fade:
                ApplyFadeAnimation(element, newText, updateText);
                break;
            case AnimationType.Scale:
                ApplyScaleAnimation(element, newText, updateText);
                break;
            case AnimationType.Glow:
                ApplyGlowAnimation(element);
                break;
            case AnimationType.Bounce:
                ApplyBounceInAnimation(element);
                break;
            default:
                updateText(newText);
                break;
        }
    }
}
