# 极简数字时钟 - Windows 11 现代化时钟应用

一个使用 VB.NET 和 WPF 开发的现代化数字时钟应用，专为 Windows 11 设计，具有 Fluent Design 效果和极简界面。

## 🌟 主要特性

### 核心功能
- ⏰ **实时时间显示** - 精确到秒的时间显示
- 📅 **日期显示** - 完整的日期信息，包含星期
- 🕐 **12/24小时制切换** - 支持两种时间格式
- 🎨 **个性化定制** - 字体、颜色、大小自由调整
- 🔍 **透明度调节** - 30%-100% 透明度范围
- 📌 **窗口置顶** - 可选的窗口置顶功能

### 现代化设计
- 🎭 **Fluent Design 亚克力效果** - Windows 11 原生视觉效果
- 🌓 **动态主题适应** - 自动适应系统深色/浅色主题
- 🖱️ **无边框可拖动设计** - 简洁美观的界面
- 💫 **平滑动画效果** - 鼠标悬停时的控制面板动画

### 系统集成
- 🚀 **开机自启** - 可选的开机自动启动
- 🔥 **全局热键** - Ctrl+Alt+T 快速显示/隐藏
- 📍 **系统托盘** - 最小化到系统托盘
- 🖱️ **右键菜单** - 快速访问常用功能

## 🛠️ 技术实现

- **框架**: .NET 6 + WPF
- **语言**: Visual Basic .NET
- **UI**: XAML + Fluent Design
- **系统集成**: Windows API + 注册表
- **内存占用**: < 1MB (优化后)

## 📋 系统要求

- Windows 10 1803+ (推荐 Windows 11)
- .NET 6 Runtime
- 支持 DWM 合成的显卡

## 🚀 快速开始

### 编译和运行

1. **克隆项目**
   ```bash
   git clone [项目地址]
   cd time
   ```

2. **使用批处理脚本运行**
   ```bash
   # 开发模式运行
   run.bat

   # 构建发布版本
   build.bat
   ```

3. **手动编译**
   ```bash
   # 还原依赖
   dotnet restore

   # 运行项目
   dotnet run

   # 构建发布版本
   dotnet build --configuration Release
   ```

### 使用说明

1. **基本操作**
   - 拖动窗口：点击时钟区域拖动
   - 显示控制面板：鼠标悬停在窗口上
   - 右键菜单：右键点击窗口

2. **快捷键**
   - `Ctrl+Alt+T`: 显示/隐藏窗口

3. **设置选项**
   - 字体：5种预设现代字体
   - 颜色：7种预设颜色方案
   - 大小：20-100像素范围
   - 透明度：30%-100%范围

## 📁 项目结构

```
DigitalClock/
├── App.xaml                    # 应用程序定义
├── App.xaml.vb                 # 应用程序逻辑
├── MainWindow.xaml             # 主窗口界面
├── MainWindow.xaml.vb          # 主窗口逻辑
├── SettingsWindow.xaml         # 设置窗口界面
├── SettingsWindow.xaml.vb      # 设置窗口逻辑
├── SettingsManager.vb          # 设置管理器
├── FluentDesignHelper.vb       # Fluent Design 效果
├── SystemIntegrationHelper.vb  # 系统集成功能
├── Styles/
│   └── GlobalStyles.xaml       # 全局样式定义
├── Resources/
│   └── clock.ico              # 应用程序图标
├── build.bat                  # 构建脚本
├── run.bat                    # 运行脚本
└── DigitalClock.vbproj        # 项目文件
```

## 🎯 功能特色

### 智能主题适应
应用程序会自动检测系统主题设置，并相应调整界面颜色：
- **深色主题**: 深色半透明背景，白色边框
- **浅色主题**: 浅色半透明背景，深色边框

### 高级视觉效果
- **亚克力背景**: 在支持的系统上启用真正的亚克力效果
- **模糊后备**: 在不支持亚克力的系统上使用模糊效果
- **阴影效果**: 文字阴影增强可读性

### 性能优化
- 低内存占用 (< 1MB)
- 高效的定时器更新
- 最小化的系统资源使用

## 🔧 开发说明

### 添加新功能
1. 在相应的类中添加功能代码
2. 更新 `SettingsManager` 以保存新设置
3. 在 `SettingsWindow` 中添加UI控件
4. 更新 `MainWindow` 以应用新设置

### 自定义样式
编辑 `Styles/GlobalStyles.xaml` 文件来修改应用程序的视觉样式。

### 系统集成
`SystemIntegrationHelper` 类提供了各种系统集成功能，包括：
- 注册表操作
- 全局热键注册
- 开机自启设置
- 管理员权限检查

## 🐛 故障排除

### 常见问题

1. **亚克力效果不显示**
   - 确保系统支持 DWM 合成
   - 检查 Windows 版本 (需要 Windows 10 1803+)

2. **热键不工作**
   - 检查是否有其他应用程序占用了相同热键
   - 尝试以管理员权限运行

3. **开机自启失败**
   - 确保有足够的权限修改注册表
   - 检查防病毒软件是否阻止了注册表修改

## 📝 更新日志

### v1.0.0
- ✅ 基本时钟功能
- ✅ Fluent Design 效果
- ✅ 设置系统
- ✅ 系统托盘集成
- ✅ 全局热键支持
- ✅ 开机自启功能

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

---

**享受您的极简数字时钟体验！** ⏰✨