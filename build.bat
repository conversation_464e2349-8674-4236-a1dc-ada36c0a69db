@echo off
echo 正在构建极简数字时钟应用...
echo.

REM 检查是否安装了.NET 6
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到 .NET 6 SDK
    echo 请从 https://dotnet.microsoft.com/download 下载并安装 .NET 6 SDK
    pause
    exit /b 1
)

REM 还原NuGet包
echo 正在还原NuGet包...
dotnet restore

REM 构建项目
echo 正在构建项目...
dotnet build --configuration Release

if %errorlevel% equ 0 (
    echo.
    echo 构建成功！
    echo 可执行文件位置: bin\Release\net6.0-windows\DigitalClock.exe
    echo.
    echo 按任意键运行应用程序...
    pause >nul
    start "" "bin\Release\net6.0-windows\DigitalClock.exe"
) else (
    echo.
    echo 构建失败！请检查错误信息。
    pause
)
