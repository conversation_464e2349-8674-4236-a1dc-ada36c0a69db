﻿#ExternalChecksum("..\..\..\SettingsWindow.xaml","{ff1816ec-aa5e-4d10-87f7-6f4963833460}","4F1DC56D114007EE8B6699B2C01F5A995924216F")
'------------------------------------------------------------------------------
' <auto-generated>
'     此代码由工具生成。
'     运行时版本:4.0.30319.42000
'
'     对此文件的更改可能会导致不正确的行为，并且如果
'     重新生成代码，这些更改将会丢失。
' </auto-generated>
'------------------------------------------------------------------------------

Option Strict Off
Option Explicit On

Imports System
Imports System.Diagnostics
Imports System.Windows
Imports System.Windows.Automation
Imports System.Windows.Controls
Imports System.Windows.Controls.Primitives
Imports System.Windows.Controls.Ribbon
Imports System.Windows.Data
Imports System.Windows.Documents
Imports System.Windows.Forms.Integration
Imports System.Windows.Ink
Imports System.Windows.Input
Imports System.Windows.Markup
Imports System.Windows.Media
Imports System.Windows.Media.Animation
Imports System.Windows.Media.Effects
Imports System.Windows.Media.Imaging
Imports System.Windows.Media.Media3D
Imports System.Windows.Media.TextFormatting
Imports System.Windows.Navigation
Imports System.Windows.Shapes
Imports System.Windows.Shell

Namespace DigitalClock
    
    '''<summary>
    '''SettingsWindow
    '''</summary>
    <Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>  _
    Partial Public Class SettingsWindow
        Inherits System.Windows.Window
        Implements System.Windows.Markup.IComponentConnector
        
        
        #ExternalSource("..\..\..\SettingsWindow.xaml",37)
        <System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")>  _
        Friend WithEvents FontFamilyComboBox As System.Windows.Controls.ComboBox
        
        #End ExternalSource
        
        
        #ExternalSource("..\..\..\SettingsWindow.xaml",44)
        <System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")>  _
        Friend WithEvents FontSizeSlider As System.Windows.Controls.Slider
        
        #End ExternalSource
        
        
        #ExternalSource("..\..\..\SettingsWindow.xaml",50)
        <System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")>  _
        Friend WithEvents FontSizeText As System.Windows.Controls.TextBlock
        
        #End ExternalSource
        
        
        #ExternalSource("..\..\..\SettingsWindow.xaml",59)
        <System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")>  _
        Friend WithEvents FontColorComboBox As System.Windows.Controls.ComboBox
        
        #End ExternalSource
        
        
        #ExternalSource("..\..\..\SettingsWindow.xaml",62)
        <System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")>  _
        Friend WithEvents ColorPreview As System.Windows.Shapes.Rectangle
        
        #End ExternalSource
        
        
        #ExternalSource("..\..\..\SettingsWindow.xaml",79)
        <System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")>  _
        Friend WithEvents OpacitySlider As System.Windows.Controls.Slider
        
        #End ExternalSource
        
        
        #ExternalSource("..\..\..\SettingsWindow.xaml",85)
        <System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")>  _
        Friend WithEvents OpacityText As System.Windows.Controls.TextBlock
        
        #End ExternalSource
        
        
        #ExternalSource("..\..\..\SettingsWindow.xaml",92)
        <System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")>  _
        Friend WithEvents TopMostCheckBox As System.Windows.Controls.CheckBox
        
        #End ExternalSource
        
        
        #ExternalSource("..\..\..\SettingsWindow.xaml",98)
        <System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")>  _
        Friend WithEvents AutoStartCheckBox As System.Windows.Controls.CheckBox
        
        #End ExternalSource
        
        
        #ExternalSource("..\..\..\SettingsWindow.xaml",109)
        <System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")>  _
        Friend WithEvents Hour12RadioButton As System.Windows.Controls.RadioButton
        
        #End ExternalSource
        
        
        #ExternalSource("..\..\..\SettingsWindow.xaml",115)
        <System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")>  _
        Friend WithEvents Hour24RadioButton As System.Windows.Controls.RadioButton
        
        #End ExternalSource
        
        
        #ExternalSource("..\..\..\SettingsWindow.xaml",129)
        <System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")>  _
        Friend WithEvents PreviewTextBlock As System.Windows.Controls.TextBlock
        
        #End ExternalSource
        
        
        #ExternalSource("..\..\..\SettingsWindow.xaml",146)
        <System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")>  _
        Friend WithEvents ResetButton As System.Windows.Controls.Button
        
        #End ExternalSource
        
        
        #ExternalSource("..\..\..\SettingsWindow.xaml",153)
        <System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")>  _
        Friend WithEvents ApplyButton As System.Windows.Controls.Button
        
        #End ExternalSource
        
        
        #ExternalSource("..\..\..\SettingsWindow.xaml",160)
        <System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")>  _
        Friend WithEvents OkButton As System.Windows.Controls.Button
        
        #End ExternalSource
        
        
        #ExternalSource("..\..\..\SettingsWindow.xaml",167)
        <System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")>  _
        Friend WithEvents CancelButton As System.Windows.Controls.Button
        
        #End ExternalSource
        
        Private _contentLoaded As Boolean
        
        '''<summary>
        '''InitializeComponent
        '''</summary>
        <System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")>  _
        Public Sub InitializeComponent() Implements System.Windows.Markup.IComponentConnector.InitializeComponent
            If _contentLoaded Then
                Return
            End If
            _contentLoaded = true
            Dim resourceLocater As System.Uri = New System.Uri("/SimpleDigitalClock;V1.0.0.0;component/settingswindow.xaml", System.UriKind.Relative)
            
            #ExternalSource("..\..\..\SettingsWindow.xaml",1)
            System.Windows.Application.LoadComponent(Me, resourceLocater)
            
            #End ExternalSource
        End Sub
        
        <System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0"),  _
         System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never),  _
         System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes"),  _
         System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity"),  _
         System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")>  _
        Sub System_Windows_Markup_IComponentConnector_Connect(ByVal connectionId As Integer, ByVal target As Object) Implements System.Windows.Markup.IComponentConnector.Connect
            If (connectionId = 1) Then
                Me.FontFamilyComboBox = CType(target,System.Windows.Controls.ComboBox)
                
                #ExternalSource("..\..\..\SettingsWindow.xaml",39)
                AddHandler Me.FontFamilyComboBox.SelectionChanged, New System.Windows.Controls.SelectionChangedEventHandler(AddressOf Me.FontFamilyComboBox_SelectionChanged)
                
                #End ExternalSource
                Return
            End If
            If (connectionId = 2) Then
                Me.FontSizeSlider = CType(target,System.Windows.Controls.Slider)
                
                #ExternalSource("..\..\..\SettingsWindow.xaml",49)
                AddHandler Me.FontSizeSlider.ValueChanged, New System.Windows.RoutedPropertyChangedEventHandler(Of Double)(AddressOf Me.FontSizeSlider_ValueChanged)
                
                #End ExternalSource
                Return
            End If
            If (connectionId = 3) Then
                Me.FontSizeText = CType(target,System.Windows.Controls.TextBlock)
                Return
            End If
            If (connectionId = 4) Then
                Me.FontColorComboBox = CType(target,System.Windows.Controls.ComboBox)
                
                #ExternalSource("..\..\..\SettingsWindow.xaml",61)
                AddHandler Me.FontColorComboBox.SelectionChanged, New System.Windows.Controls.SelectionChangedEventHandler(AddressOf Me.FontColorComboBox_SelectionChanged)
                
                #End ExternalSource
                Return
            End If
            If (connectionId = 5) Then
                Me.ColorPreview = CType(target,System.Windows.Shapes.Rectangle)
                Return
            End If
            If (connectionId = 6) Then
                Me.OpacitySlider = CType(target,System.Windows.Controls.Slider)
                
                #ExternalSource("..\..\..\SettingsWindow.xaml",84)
                AddHandler Me.OpacitySlider.ValueChanged, New System.Windows.RoutedPropertyChangedEventHandler(Of Double)(AddressOf Me.OpacitySlider_ValueChanged)
                
                #End ExternalSource
                Return
            End If
            If (connectionId = 7) Then
                Me.OpacityText = CType(target,System.Windows.Controls.TextBlock)
                Return
            End If
            If (connectionId = 8) Then
                Me.TopMostCheckBox = CType(target,System.Windows.Controls.CheckBox)
                
                #ExternalSource("..\..\..\SettingsWindow.xaml",95)
                AddHandler Me.TopMostCheckBox.Checked, New System.Windows.RoutedEventHandler(AddressOf Me.TopMostCheckBox_Checked)
                
                #End ExternalSource
                
                #ExternalSource("..\..\..\SettingsWindow.xaml",96)
                AddHandler Me.TopMostCheckBox.Unchecked, New System.Windows.RoutedEventHandler(AddressOf Me.TopMostCheckBox_Unchecked)
                
                #End ExternalSource
                Return
            End If
            If (connectionId = 9) Then
                Me.AutoStartCheckBox = CType(target,System.Windows.Controls.CheckBox)
                
                #ExternalSource("..\..\..\SettingsWindow.xaml",101)
                AddHandler Me.AutoStartCheckBox.Checked, New System.Windows.RoutedEventHandler(AddressOf Me.AutoStartCheckBox_Checked)
                
                #End ExternalSource
                
                #ExternalSource("..\..\..\SettingsWindow.xaml",102)
                AddHandler Me.AutoStartCheckBox.Unchecked, New System.Windows.RoutedEventHandler(AddressOf Me.AutoStartCheckBox_Unchecked)
                
                #End ExternalSource
                Return
            End If
            If (connectionId = 10) Then
                Me.Hour12RadioButton = CType(target,System.Windows.Controls.RadioButton)
                
                #ExternalSource("..\..\..\SettingsWindow.xaml",113)
                AddHandler Me.Hour12RadioButton.Checked, New System.Windows.RoutedEventHandler(AddressOf Me.Hour12RadioButton_Checked)
                
                #End ExternalSource
                Return
            End If
            If (connectionId = 11) Then
                Me.Hour24RadioButton = CType(target,System.Windows.Controls.RadioButton)
                
                #ExternalSource("..\..\..\SettingsWindow.xaml",119)
                AddHandler Me.Hour24RadioButton.Checked, New System.Windows.RoutedEventHandler(AddressOf Me.Hour24RadioButton_Checked)
                
                #End ExternalSource
                Return
            End If
            If (connectionId = 12) Then
                Me.PreviewTextBlock = CType(target,System.Windows.Controls.TextBlock)
                Return
            End If
            If (connectionId = 13) Then
                Me.ResetButton = CType(target,System.Windows.Controls.Button)
                
                #ExternalSource("..\..\..\SettingsWindow.xaml",151)
                AddHandler Me.ResetButton.Click, New System.Windows.RoutedEventHandler(AddressOf Me.ResetButton_Click)
                
                #End ExternalSource
                Return
            End If
            If (connectionId = 14) Then
                Me.ApplyButton = CType(target,System.Windows.Controls.Button)
                
                #ExternalSource("..\..\..\SettingsWindow.xaml",158)
                AddHandler Me.ApplyButton.Click, New System.Windows.RoutedEventHandler(AddressOf Me.ApplyButton_Click)
                
                #End ExternalSource
                Return
            End If
            If (connectionId = 15) Then
                Me.OkButton = CType(target,System.Windows.Controls.Button)
                
                #ExternalSource("..\..\..\SettingsWindow.xaml",165)
                AddHandler Me.OkButton.Click, New System.Windows.RoutedEventHandler(AddressOf Me.OkButton_Click)
                
                #End ExternalSource
                Return
            End If
            If (connectionId = 16) Then
                Me.CancelButton = CType(target,System.Windows.Controls.Button)
                
                #ExternalSource("..\..\..\SettingsWindow.xaml",171)
                AddHandler Me.CancelButton.Click, New System.Windows.RoutedEventHandler(AddressOf Me.CancelButton_Click)
                
                #End ExternalSource
                Return
            End If
            Me._contentLoaded = true
        End Sub
    End Class
End Namespace

