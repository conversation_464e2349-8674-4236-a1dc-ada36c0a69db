<Window x:Class="DigitalClock.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="极简数字时钟" 
        Height="200" 
        Width="400"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        Topmost="False"
        ResizeMode="NoResize"
        ShowInTaskbar="True"
        WindowStartupLocation="CenterScreen">

    <!-- 主窗口背景 -->
    <Border x:Name="MainBorder" 
            CornerRadius="10" 
            Background="#80000000"
            BorderThickness="1"
            BorderBrush="#40FFFFFF"
            MouseLeftButtonDown="Border_MouseLeftButtonDown">
        
        <!-- 亚克力效果背景 -->
        <Border.Effect>
            <BlurEffect Radius="20"/>
        </Border.Effect>
        
        <!-- 主要内容区域 -->
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- 时间显示区域 -->
            <StackPanel Grid.Row="0" 
                       VerticalAlignment="Center" 
                       HorizontalAlignment="Center">
                
                <!-- 时间文本 -->
                <TextBlock x:Name="TimeTextBlock" 
                          Text="00:00:00"
                          Style="{StaticResource ClockTextStyle}"
                          FontSize="48"
                          Margin="0,10"/>
                
                <!-- 日期文本 -->
                <TextBlock x:Name="DateTextBlock" 
                          Text="2024年1月1日 星期一"
                          Style="{StaticResource DateTextStyle}"
                          FontSize="14"
                          Margin="0,0,0,10"/>
            </StackPanel>
            
            <!-- 控制按钮区域 -->
            <StackPanel Grid.Row="1" 
                       Orientation="Horizontal" 
                       HorizontalAlignment="Center"
                       Margin="0,0,0,10"
                       x:Name="ControlPanel"
                       Opacity="0">
                
                <Button x:Name="SettingsButton" 
                       Content="设置" 
                       Style="{StaticResource ModernButtonStyle}"
                       Click="SettingsButton_Click"/>
                
                <Button x:Name="TopMostButton" 
                       Content="置顶" 
                       Style="{StaticResource ModernButtonStyle}"
                       Click="TopMostButton_Click"/>
                
                <Button x:Name="MinimizeButton" 
                       Content="最小化" 
                       Style="{StaticResource ModernButtonStyle}"
                       Click="MinimizeButton_Click"/>
                
                <Button x:Name="CloseButton" 
                       Content="关闭" 
                       Style="{StaticResource ModernButtonStyle}"
                       Click="CloseButton_Click"/>
            </StackPanel>
            
            <!-- 状态指示器 -->
            <StackPanel Grid.Row="2" 
                       Orientation="Horizontal" 
                       HorizontalAlignment="Right"
                       Margin="0,0,10,5">
                
                <Ellipse x:Name="TopMostIndicator" 
                        Width="8" 
                        Height="8" 
                        Fill="LimeGreen" 
                        Margin="5,0"
                        Visibility="Collapsed"/>
                
                <TextBlock x:Name="StatusText" 
                          Text="12H" 
                          FontSize="10" 
                          Foreground="White" 
                          Opacity="0.6"/>
            </StackPanel>
        </Grid>
        
        <!-- 鼠标悬停动画触发器 -->
        <Border.Triggers>
            <EventTrigger RoutedEvent="MouseEnter">
                <BeginStoryboard>
                    <Storyboard>
                        <DoubleAnimation Storyboard.TargetName="ControlPanel"
                                       Storyboard.TargetProperty="Opacity"
                                       To="1" Duration="0:0:0.3"/>
                    </Storyboard>
                </BeginStoryboard>
            </EventTrigger>
            <EventTrigger RoutedEvent="MouseLeave">
                <BeginStoryboard>
                    <Storyboard>
                        <DoubleAnimation Storyboard.TargetName="ControlPanel"
                                       Storyboard.TargetProperty="Opacity"
                                       To="0" Duration="0:0:0.3"/>
                    </Storyboard>
                </BeginStoryboard>
            </EventTrigger>
        </Border.Triggers>
    </Border>
    
    <!-- 右键菜单 -->
    <Window.ContextMenu>
        <ContextMenu>
            <MenuItem Header="设置" Click="SettingsMenuItem_Click"/>
            <MenuItem Header="置顶" x:Name="TopMostMenuItem" Click="TopMostMenuItem_Click"/>
            <Separator/>
            <MenuItem Header="12小时制" x:Name="Hour12MenuItem" Click="Hour12MenuItem_Click"/>
            <MenuItem Header="24小时制" x:Name="Hour24MenuItem" Click="Hour24MenuItem_Click"/>
            <Separator/>
            <MenuItem Header="开机自启" x:Name="AutoStartMenuItem" Click="AutoStartMenuItem_Click"/>
            <Separator/>
            <MenuItem Header="关于" Click="AboutMenuItem_Click"/>
            <MenuItem Header="退出" Click="ExitMenuItem_Click"/>
        </ContextMenu>
    </Window.ContextMenu>
</Window>
