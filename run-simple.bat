@echo off
echo 正在创建并运行简化版数字时钟...
echo.

REM 检查是否安装了.NET 6
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到 .NET 6 SDK
    echo 请从 https://dotnet.microsoft.com/download 下载并安装 .NET 6 SDK
    pause
    exit /b 1
)

echo 您的.NET运行时版本：
dotnet --list-runtimes | findstr "Microsoft.WindowsDesktop.App 6"
echo.

REM 创建新的WPF项目
echo 正在创建新的WPF项目...
if exist "SimpleClock" rmdir /s /q "SimpleClock"
dotnet new wpf -n SimpleClock -f net6.0

if %errorlevel% neq 0 (
    echo 创建项目失败！
    pause
    exit /b 1
)

cd SimpleClock

REM 替换MainWindow.xaml内容
echo 正在配置时钟界面...
(
echo ^<Window x:Class="SimpleClock.MainWindow"
echo         xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
echo         xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
echo         Title="极简数字时钟" Height="200" Width="400"
echo         WindowStyle="None" AllowsTransparency="True" Background="Transparent"
echo         WindowStartupLocation="CenterScreen"^>
echo     ^<Border CornerRadius="10" Background="#80000000" BorderThickness="1" BorderBrush="#40FFFFFF" MouseLeftButtonDown="Border_MouseLeftButtonDown"^>
echo         ^<Grid^>
echo             ^<StackPanel VerticalAlignment="Center" HorizontalAlignment="Center"^>
echo                 ^<TextBlock x:Name="TimeTextBlock" Text="00:00:00" FontFamily="Segoe UI" FontSize="48" FontWeight="Light" Foreground="White" HorizontalAlignment="Center" Margin="0,10"^>
echo                     ^<TextBlock.Effect^>
echo                         ^<DropShadowEffect Color="Black" Direction="270" ShadowDepth="2" BlurRadius="4" Opacity="0.5"/^>
echo                     ^</TextBlock.Effect^>
echo                 ^</TextBlock^>
echo                 ^<TextBlock x:Name="DateTextBlock" Text="2024年1月1日 星期一" FontFamily="Segoe UI" FontSize="14" Foreground="White" HorizontalAlignment="Center" Opacity="0.8" Margin="0,0,0,10"/^>
echo             ^</StackPanel^>
echo         ^</Grid^>
echo     ^</Border^>
echo     ^<Window.ContextMenu^>
echo         ^<ContextMenu^>
echo             ^<MenuItem Header="置顶" x:Name="TopMostMenuItem" Click="TopMostMenuItem_Click"/^>
echo             ^<MenuItem Header="12小时制" Click="Hour12MenuItem_Click"/^>
echo             ^<MenuItem Header="24小时制" Click="Hour24MenuItem_Click"/^>
echo             ^<MenuItem Header="退出" Click="ExitMenuItem_Click"/^>
echo         ^</ContextMenu^>
echo     ^</Window.ContextMenu^>
echo ^</Window^>
) > MainWindow.xaml

REM 替换MainWindow.xaml.cs内容
(
echo using System;
echo using System.Windows;
echo using System.Windows.Input;
echo using System.Windows.Threading;
echo.
echo namespace SimpleClock
echo {
echo     public partial class MainWindow : Window
echo     {
echo         private DispatcherTimer clockTimer;
echo         private bool is24HourFormat = true;
echo.
echo         public MainWindow^(^)
echo         {
echo             InitializeComponent^(^);
echo             InitializeClock^(^);
echo         }
echo.
echo         private void InitializeClock^(^)
echo         {
echo             clockTimer = new DispatcherTimer^(^);
echo             clockTimer.Interval = TimeSpan.FromSeconds^(1^);
echo             clockTimer.Tick += ClockTimer_Tick;
echo             clockTimer.Start^(^);
echo             UpdateTimeDisplay^(^);
echo         }
echo.
echo         private void ClockTimer_Tick^(object sender, EventArgs e^)
echo         {
echo             UpdateTimeDisplay^(^);
echo         }
echo.
echo         private void UpdateTimeDisplay^(^)
echo         {
echo             var now = DateTime.Now;
echo             if ^(is24HourFormat^)
echo             {
echo                 TimeTextBlock.Text = now.ToString^("HH:mm:ss"^);
echo             }
echo             else
echo             {
echo                 TimeTextBlock.Text = now.ToString^("hh:mm:ss tt"^);
echo             }
echo             DateTextBlock.Text = now.ToString^("yyyy年MM月dd日 dddd"^);
echo         }
echo.
echo         private void Border_MouseLeftButtonDown^(object sender, MouseButtonEventArgs e^)
echo         {
echo             if ^(e.ButtonState == MouseButtonState.Pressed^)
echo                 this.DragMove^(^);
echo         }
echo.
echo         private void TopMostMenuItem_Click^(object sender, RoutedEventArgs e^)
echo         {
echo             this.Topmost = !this.Topmost;
echo             TopMostMenuItem.Header = this.Topmost ? "取消置顶" : "置顶";
echo         }
echo.
echo         private void Hour12MenuItem_Click^(object sender, RoutedEventArgs e^)
echo         {
echo             is24HourFormat = false;
echo             UpdateTimeDisplay^(^);
echo         }
echo.
echo         private void Hour24MenuItem_Click^(object sender, RoutedEventArgs e^)
echo         {
echo             is24HourFormat = true;
echo             UpdateTimeDisplay^(^);
echo         }
echo.
echo         private void ExitMenuItem_Click^(object sender, RoutedEventArgs e^)
echo         {
echo             Application.Current.Shutdown^(^);
echo         }
echo     }
echo }
) > MainWindow.xaml.cs

echo 正在构建项目...
dotnet build

if %errorlevel% neq 0 (
    echo 构建失败！
    pause
    exit /b 1
)

echo.
echo 构建成功！正在运行极简数字时钟...
echo.
echo 使用说明：
echo - 拖动时钟移动窗口
echo - 右键点击访问菜单
echo - 可以设置置顶、时间格式等
echo.

dotnet run

cd ..
pause
