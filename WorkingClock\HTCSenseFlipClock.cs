using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Shapes;

namespace WorkingClock;

/// <summary>
/// HTC Sense风格的翻页时钟控件
/// </summary>
public class HTCSenseFlipClock : UserControl
{
    private Grid mainGrid;
    private Border topHalf;
    private Border bottomHalf;
    private Border flipCard;
    private TextBlock currentText;
    private TextBlock nextText;
    private TextBlock flipText;
    
    private string currentValue = "";
    private bool isAnimating = false;

    public HTCSenseFlipClock()
    {
        InitializeComponent();
    }

    /// <summary>
    /// 初始化组件
    /// </summary>
    private void InitializeComponent()
    {
        Width = 120;
        Height = 80;
        
        // 主网格
        mainGrid = new Grid();
        Content = mainGrid;

        // 创建翻页卡片结构
        CreateFlipCardStructure();
        
        // 设置默认样式
        ApplyDefaultStyle();
    }

    /// <summary>
    /// 创建翻页卡片结构
    /// </summary>
    private void CreateFlipCardStructure()
    {
        // 上半部分（固定）
        topHalf = new Border
        {
            Background = CreateCardGradient(true),
            BorderBrush = new SolidColorBrush(Color.FromArgb(100, 255, 255, 255)),
            BorderThickness = new Thickness(1, 1, 1, 0),
            CornerRadius = new CornerRadius(8, 8, 0, 0),
            ClipToBounds = true,
            VerticalAlignment = VerticalAlignment.Top,
            Height = 40
        };

        currentText = new TextBlock
        {
            Text = "00",
            FontSize = 32,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush(Colors.White),
            HorizontalAlignment = HorizontalAlignment.Center,
            VerticalAlignment = VerticalAlignment.Bottom,
            Margin = new Thickness(0, 0, 0, -2),
            Effect = new System.Windows.Media.Effects.DropShadowEffect
            {
                Color = Colors.Black,
                BlurRadius = 2,
                ShadowDepth = 1,
                Opacity = 0.5
            }
        };

        topHalf.Child = currentText;
        mainGrid.Children.Add(topHalf);

        // 下半部分（固定）
        bottomHalf = new Border
        {
            Background = CreateCardGradient(false),
            BorderBrush = new SolidColorBrush(Color.FromArgb(100, 255, 255, 255)),
            BorderThickness = new Thickness(1, 0, 1, 1),
            CornerRadius = new CornerRadius(0, 0, 8, 8),
            ClipToBounds = true,
            VerticalAlignment = VerticalAlignment.Bottom,
            Height = 40
        };

        nextText = new TextBlock
        {
            Text = "00",
            FontSize = 32,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush(Colors.White),
            HorizontalAlignment = HorizontalAlignment.Center,
            VerticalAlignment = VerticalAlignment.Top,
            Margin = new Thickness(0, -2, 0, 0),
            Effect = new System.Windows.Media.Effects.DropShadowEffect
            {
                Color = Colors.Black,
                BlurRadius = 2,
                ShadowDepth = 1,
                Opacity = 0.5
            }
        };

        bottomHalf.Child = nextText;
        mainGrid.Children.Add(bottomHalf);

        // 翻页卡片（动画）
        flipCard = new Border
        {
            Background = CreateCardGradient(true),
            BorderBrush = new SolidColorBrush(Color.FromArgb(100, 255, 255, 255)),
            BorderThickness = new Thickness(1),
            CornerRadius = new CornerRadius(8, 8, 0, 0),
            ClipToBounds = true,
            VerticalAlignment = VerticalAlignment.Top,
            Height = 40,
            RenderTransformOrigin = new Point(0.5, 1.0),
            Visibility = Visibility.Hidden
        };

        flipText = new TextBlock
        {
            Text = "00",
            FontSize = 32,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush(Colors.White),
            HorizontalAlignment = HorizontalAlignment.Center,
            VerticalAlignment = VerticalAlignment.Bottom,
            Margin = new Thickness(0, 0, 0, -2),
            Effect = new System.Windows.Media.Effects.DropShadowEffect
            {
                Color = Colors.Black,
                BlurRadius = 2,
                ShadowDepth = 1,
                Opacity = 0.5
            }
        };

        flipCard.Child = flipText;
        mainGrid.Children.Add(flipCard);

        // 添加分隔线
        var separator = new Rectangle
        {
            Height = 1,
            Fill = new SolidColorBrush(Color.FromArgb(150, 0, 0, 0)),
            VerticalAlignment = VerticalAlignment.Center,
            Margin = new Thickness(8, 0, 8, 0)
        };
        mainGrid.Children.Add(separator);
    }

    /// <summary>
    /// 创建卡片渐变背景
    /// </summary>
    private LinearGradientBrush CreateCardGradient(bool isTop)
    {
        var gradient = new LinearGradientBrush();
        gradient.StartPoint = new Point(0, 0);
        gradient.EndPoint = new Point(0, 1);

        if (isTop)
        {
            gradient.GradientStops.Add(new GradientStop(Color.FromArgb(255, 80, 80, 80), 0.0));
            gradient.GradientStops.Add(new GradientStop(Color.FromArgb(255, 60, 60, 60), 0.5));
            gradient.GradientStops.Add(new GradientStop(Color.FromArgb(255, 40, 40, 40), 1.0));
        }
        else
        {
            gradient.GradientStops.Add(new GradientStop(Color.FromArgb(255, 40, 40, 40), 0.0));
            gradient.GradientStops.Add(new GradientStop(Color.FromArgb(255, 30, 30, 30), 0.5));
            gradient.GradientStops.Add(new GradientStop(Color.FromArgb(255, 20, 20, 20), 1.0));
        }

        return gradient;
    }

    /// <summary>
    /// 应用默认样式
    /// </summary>
    private void ApplyDefaultStyle()
    {
        // 添加整体阴影效果
        Effect = new System.Windows.Media.Effects.DropShadowEffect
        {
            Color = Colors.Black,
            BlurRadius = 10,
            ShadowDepth = 5,
            Opacity = 0.3
        };
    }

    /// <summary>
    /// 设置显示值（带动画）
    /// </summary>
    public void SetValue(string newValue)
    {
        if (newValue == currentValue || isAnimating) return;

        if (string.IsNullOrEmpty(currentValue))
        {
            // 首次设置，不播放动画
            currentValue = newValue;
            currentText.Text = newValue;
            nextText.Text = newValue;
            return;
        }

        // 播放翻页动画
        PlayFlipAnimation(newValue);
    }

    /// <summary>
    /// 播放HTC Sense翻页动画
    /// </summary>
    private void PlayFlipAnimation(string newValue)
    {
        if (isAnimating) return;
        
        isAnimating = true;

        // 设置翻页卡片的初始状态
        flipText.Text = currentValue;
        flipCard.Visibility = Visibility.Visible;
        flipCard.RenderTransform = new RotateTransform(0);

        // 创建动画故事板
        var storyboard = new Storyboard();

        // 第一阶段：翻页卡片向下翻转到90度
        var flipRotation = new DoubleAnimation
        {
            From = 0,
            To = 90,
            Duration = TimeSpan.FromMilliseconds(300),
            EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseIn }
        };

        Storyboard.SetTarget(flipRotation, flipCard);
        Storyboard.SetTargetProperty(flipRotation, new PropertyPath("RenderTransform.Angle"));
        storyboard.Children.Add(flipRotation);

        // 中间阶段：更换文本和样式
        storyboard.Completed += (s, e) =>
        {
            // 更新翻页卡片为下半部分样式
            flipCard.Background = CreateCardGradient(false);
            flipCard.CornerRadius = new CornerRadius(0, 0, 8, 8);
            flipCard.BorderThickness = new Thickness(1, 0, 1, 1);
            flipCard.VerticalAlignment = VerticalAlignment.Bottom;
            flipCard.RenderTransformOrigin = new Point(0.5, 0.0);
            
            flipText.Text = newValue;
            flipText.VerticalAlignment = VerticalAlignment.Top;
            flipText.Margin = new Thickness(0, -2, 0, 0);

            // 第二阶段：从-90度翻转到0度
            var storyboard2 = new Storyboard();
            flipCard.RenderTransform = new RotateTransform(-90);

            var flipRotation2 = new DoubleAnimation
            {
                From = -90,
                To = 0,
                Duration = TimeSpan.FromMilliseconds(300),
                EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseOut }
            };

            Storyboard.SetTarget(flipRotation2, flipCard);
            Storyboard.SetTargetProperty(flipRotation2, new PropertyPath("RenderTransform.Angle"));
            storyboard2.Children.Add(flipRotation2);

            storyboard2.Completed += (s2, e2) =>
            {
                // 动画完成，更新状态
                currentValue = newValue;
                currentText.Text = newValue;
                nextText.Text = newValue;
                flipCard.Visibility = Visibility.Hidden;
                
                // 重置翻页卡片状态
                flipCard.Background = CreateCardGradient(true);
                flipCard.CornerRadius = new CornerRadius(8, 8, 0, 0);
                flipCard.BorderThickness = new Thickness(1, 1, 1, 0);
                flipCard.VerticalAlignment = VerticalAlignment.Top;
                flipCard.RenderTransformOrigin = new Point(0.5, 1.0);
                flipText.VerticalAlignment = VerticalAlignment.Bottom;
                flipText.Margin = new Thickness(0, 0, 0, -2);
                
                isAnimating = false;
            };

            storyboard2.Begin();
        };

        storyboard.Begin();
    }

    /// <summary>
    /// 设置字体样式
    /// </summary>
    public void SetFontStyle(FontFamily fontFamily, double fontSize, Brush foreground)
    {
        currentText.FontFamily = fontFamily;
        currentText.FontSize = fontSize;
        currentText.Foreground = foreground;

        nextText.FontFamily = fontFamily;
        nextText.FontSize = fontSize;
        nextText.Foreground = foreground;

        flipText.FontFamily = fontFamily;
        flipText.FontSize = fontSize;
        flipText.Foreground = foreground;
    }

    /// <summary>
    /// 设置卡片颜色主题
    /// </summary>
    public void SetColorTheme(Color primaryColor, Color secondaryColor)
    {
        topHalf.Background = CreateCustomGradient(primaryColor, true);
        bottomHalf.Background = CreateCustomGradient(secondaryColor, false);
    }

    /// <summary>
    /// 创建自定义渐变
    /// </summary>
    private LinearGradientBrush CreateCustomGradient(Color baseColor, bool isTop)
    {
        var gradient = new LinearGradientBrush();
        gradient.StartPoint = new Point(0, 0);
        gradient.EndPoint = new Point(0, 1);

        var factor = isTop ? 1.2 : 0.8;
        var lightColor = Color.FromArgb(baseColor.A, 
            (byte)Math.Min(255, baseColor.R * factor),
            (byte)Math.Min(255, baseColor.G * factor),
            (byte)Math.Min(255, baseColor.B * factor));
        
        var darkColor = Color.FromArgb(baseColor.A,
            (byte)(baseColor.R * 0.6),
            (byte)(baseColor.G * 0.6),
            (byte)(baseColor.B * 0.6));

        if (isTop)
        {
            gradient.GradientStops.Add(new GradientStop(lightColor, 0.0));
            gradient.GradientStops.Add(new GradientStop(baseColor, 0.5));
            gradient.GradientStops.Add(new GradientStop(darkColor, 1.0));
        }
        else
        {
            gradient.GradientStops.Add(new GradientStop(darkColor, 0.0));
            gradient.GradientStops.Add(new GradientStop(baseColor, 0.5));
            gradient.GradientStops.Add(new GradientStop(lightColor, 1.0));
        }

        return gradient;
    }
}
