using System;
using System.Collections.Generic;
using System.IO;
using System.Windows;
using System.Windows.Media;
using System.Linq;
using System.Drawing.Text;
using System.Drawing;

namespace WorkingClock;

/// <summary>
/// 字体信息类
/// </summary>
public class FontInfo
{
    public string Name { get; set; } = "";
    public string DisplayName { get; set; } = "";
    public string FilePath { get; set; } = "";
    public bool IsSystemFont { get; set; } = true;
    public bool IsMonospace { get; set; } = false;
    public bool IsDigital { get; set; } = false;
    public string Category { get; set; } = "通用";
}

/// <summary>
/// 字体管理器 - 处理系统字体和自定义字体加载
/// </summary>
public static class FontManager
{
    private static readonly Dictionary<string, FontFamily> customFonts = new();
    private static readonly Dictionary<string, FontInfo> systemFonts = new();
    private static bool fontsLoaded = false;

    /// <summary>
    /// 获取所有可用字体（包括系统字体和自定义字体）
    /// </summary>
    public static List<string> GetAvailableFonts()
    {
        LoadAllFonts();

        var fonts = new List<string>();

        // 添加推荐的数字时钟字体（优先显示）
        var recommendedFonts = new List<string>
        {
            "DS-Digital", // 自定义LED字体
            "Consolas", // 等宽字体
            "Courier New", // 等宽字体
            "Arial", // 经典字体
            "Segoe UI", // 系统默认
            "Microsoft YaHei", // 中文支持
        };

        foreach (var font in recommendedFonts)
        {
            if (customFonts.ContainsKey(font) || systemFonts.ContainsKey(font))
            {
                fonts.Add(font);
            }
        }

        // 添加其他系统字体（按类别排序）
        var otherFonts = systemFonts.Values
            .Where(f => !recommendedFonts.Contains(f.Name))
            .OrderBy(f => f.Category)
            .ThenBy(f => f.DisplayName)
            .Select(f => f.Name)
            .ToList();

        fonts.AddRange(otherFonts);

        return fonts.Distinct().ToList();
    }

    /// <summary>
    /// 获取分类的字体列表
    /// </summary>
    public static Dictionary<string, List<string>> GetCategorizedFonts()
    {
        LoadAllFonts();

        var categorized = new Dictionary<string, List<string>>();

        // 添加推荐字体类别
        categorized["🔤 推荐数字字体"] = new List<string>();
        if (customFonts.ContainsKey("DS-Digital")) categorized["🔤 推荐数字字体"].Add("DS-Digital");
        if (systemFonts.ContainsKey("Consolas")) categorized["🔤 推荐数字字体"].Add("Consolas");
        if (systemFonts.ContainsKey("Courier New")) categorized["🔤 推荐数字字体"].Add("Courier New");

        // 按类别分组系统字体
        foreach (var fontInfo in systemFonts.Values)
        {
            var category = $"📁 {fontInfo.Category}";
            if (!categorized.ContainsKey(category))
                categorized[category] = new List<string>();

            categorized[category].Add(fontInfo.Name);
        }

        // 排序每个类别内的字体
        foreach (var category in categorized.Keys.ToList())
        {
            categorized[category] = categorized[category].OrderBy(f => f).ToList();
        }

        return categorized;
    }

    /// <summary>
    /// 获取字体族
    /// </summary>
    public static FontFamily GetFontFamily(string fontName)
    {
        LoadAllFonts();

        // 首先检查自定义字体
        if (customFonts.ContainsKey(fontName))
        {
            return customFonts[fontName];
        }

        // 检查系统字体
        if (systemFonts.ContainsKey(fontName))
        {
            try
            {
                return new FontFamily(fontName);
            }
            catch
            {
                // 如果系统字体加载失败，尝试使用文件路径
                var fontInfo = systemFonts[fontName];
                if (!string.IsNullOrEmpty(fontInfo.FilePath) && File.Exists(fontInfo.FilePath))
                {
                    try
                    {
                        var fontUri = new Uri($"file:///{fontInfo.FilePath.Replace('\\', '/')}");
                        return new FontFamily(fontUri, fontName);
                    }
                    catch
                    {
                        // 忽略错误，使用默认字体
                    }
                }
            }
        }

        // 如果字体不存在，返回默认字体
        try
        {
            return new FontFamily(fontName);
        }
        catch
        {
            return new FontFamily("Segoe UI");
        }
    }

    /// <summary>
    /// 加载所有字体（系统字体和自定义字体）
    /// </summary>
    private static void LoadAllFonts()
    {
        if (fontsLoaded) return;

        try
        {
            // 加载系统字体
            LoadSystemFonts();

            // 加载自定义字体
            LoadCustomFonts();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"加载字体时出错: {ex.Message}");
        }

        fontsLoaded = true;
    }

    /// <summary>
    /// 加载系统字体
    /// </summary>
    private static void LoadSystemFonts()
    {
        try
        {
            // 方法1: 使用WPF字体枚举
            foreach (var fontFamily in Fonts.SystemFontFamilies)
            {
                var fontName = fontFamily.Source;
                if (string.IsNullOrEmpty(fontName)) continue;

                var fontInfo = new FontInfo
                {
                    Name = fontName,
                    DisplayName = GetFontDisplayName(fontName),
                    IsSystemFont = true,
                    IsMonospace = IsMonospaceFont(fontName),
                    IsDigital = IsDigitalFont(fontName),
                    Category = GetFontCategory(fontName)
                };

                systemFonts[fontName] = fontInfo;
            }

            // 方法2: 扫描Windows字体目录
            ScanWindowsFontsDirectory();

            System.Diagnostics.Debug.WriteLine($"已加载 {systemFonts.Count} 个系统字体");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"加载系统字体失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 扫描Windows字体目录
    /// </summary>
    private static void ScanWindowsFontsDirectory()
    {
        try
        {
            var windowsFontsPath = @"C:\Windows\Fonts";
            if (!Directory.Exists(windowsFontsPath)) return;

            var fontFiles = Directory.GetFiles(windowsFontsPath, "*.ttf")
                .Concat(Directory.GetFiles(windowsFontsPath, "*.otf"))
                .Concat(Directory.GetFiles(windowsFontsPath, "*.ttc"));

            foreach (var fontFile in fontFiles)
            {
                try
                {
                    var fileName = Path.GetFileNameWithoutExtension(fontFile);

                    // 跳过已存在的字体
                    if (systemFonts.ContainsKey(fileName)) continue;

                    // 尝试从文件名推断字体名称
                    var fontName = GetFontNameFromFile(fontFile);
                    if (string.IsNullOrEmpty(fontName)) fontName = fileName;

                    // 跳过已存在的字体
                    if (systemFonts.ContainsKey(fontName)) continue;

                    var fontInfo = new FontInfo
                    {
                        Name = fontName,
                        DisplayName = GetFontDisplayName(fontName),
                        FilePath = fontFile,
                        IsSystemFont = true,
                        IsMonospace = IsMonospaceFont(fontName),
                        IsDigital = IsDigitalFont(fontName),
                        Category = GetFontCategory(fontName)
                    };

                    systemFonts[fontName] = fontInfo;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"处理字体文件失败 {fontFile}: {ex.Message}");
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"扫描Windows字体目录失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 加载自定义字体
    /// </summary>
    private static void LoadCustomFonts()
    {
        try
        {
            var fontsPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Fonts");

            if (Directory.Exists(fontsPath))
            {
                var fontFiles = Directory.GetFiles(fontsPath, "*.ttf")
                    .Concat(Directory.GetFiles(fontsPath, "*.otf"));

                foreach (var fontFile in fontFiles)
                {
                    try
                    {
                        var fontName = Path.GetFileNameWithoutExtension(fontFile);
                        var fontUri = new Uri($"file:///{fontFile.Replace('\\', '/')}");
                        var fontFamily = new FontFamily(fontUri, fontName);

                        // 特殊处理DS-Digital字体
                        if (fontName.Contains("DS-Digital") || fontName.Contains("DS_Digital"))
                        {
                            customFonts["DS-Digital"] = fontFamily;
                        }
                        else
                        {
                            customFonts[fontName] = fontFamily;
                        }

                        System.Diagnostics.Debug.WriteLine($"已加载自定义字体: {fontName}");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"加载字体失败 {fontFile}: {ex.Message}");
                    }
                }
            }

            // 如果没有找到DS-Digital字体文件，创建一个模拟的LED字体
            if (!customFonts.ContainsKey("DS-Digital"))
            {
                CreateSimulatedDigitalFont();
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"加载自定义字体时出错: {ex.Message}");
        }
    }

    /// <summary>
    /// 创建模拟的数字字体（当没有DS-Digital字体文件时）
    /// </summary>
    private static void CreateSimulatedDigitalFont()
    {
        try
        {
            // 使用Consolas作为DS-Digital的替代，因为它是等宽字体，比较接近数字显示效果
            customFonts["DS-Digital"] = new FontFamily("Consolas");
            System.Diagnostics.Debug.WriteLine("使用Consolas作为DS-Digital字体的替代");
        }
        catch
        {
            // 如果Consolas也不可用，使用Courier New
            customFonts["DS-Digital"] = new FontFamily("Courier New");
            System.Diagnostics.Debug.WriteLine("使用Courier New作为DS-Digital字体的替代");
        }
    }

    /// <summary>
    /// 检查是否为数字字体
    /// </summary>
    public static bool IsDigitalFont(string fontName)
    {
        return fontName.Contains("DS-Digital") || 
               fontName.Contains("Digital") || 
               fontName.Contains("LED") || 
               fontName.Contains("LCD");
    }

    /// <summary>
    /// 获取字体显示名称
    /// </summary>
    public static string GetFontDisplayName(string fontName)
    {
        return fontName switch
        {
            "DS-Digital" => "DS-Digital (LED数字)",
            "Segoe UI" => "Segoe UI (默认)",
            "Consolas" => "Consolas (等宽)",
            "Arial" => "Arial (经典)",
            "Times New Roman" => "Times New Roman (衬线)",
            "Calibri" => "Calibri (现代)",
            "Microsoft YaHei" => "Microsoft YaHei (微软雅黑)",
            "SimSun" => "SimSun (宋体)",
            "Courier New" => "Courier New (打字机)",
            "Verdana" => "Verdana (清晰)",
            "Tahoma" => "Tahoma (紧凑)",
            _ => fontName
        };
    }

    /// <summary>
    /// 获取推荐的字体大小
    /// </summary>
    public static double GetRecommendedFontSize(string fontName, double currentSize)
    {
        // DS-Digital字体通常需要稍大一些的尺寸来获得最佳效果
        if (IsDigitalFont(fontName))
        {
            return Math.Max(currentSize * 1.1, currentSize + 4);
        }
        
        return currentSize;
    }

    /// <summary>
    /// 获取字体的建议颜色
    /// </summary>
    public static string GetRecommendedFontColor(string fontName)
    {
        // DS-Digital字体通常用绿色或红色来模拟LED效果
        if (IsDigitalFont(fontName))
        {
            return "LimeGreen"; // LED绿色
        }
        
        return "White"; // 默认白色
    }

    /// <summary>
    /// 检查字体文件是否存在
    /// </summary>
    public static bool IsFontFileAvailable(string fontName)
    {
        if (fontName == "DS-Digital")
        {
            var fontsPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Fonts");
            var dsDigitalPath = Path.Combine(fontsPath, "DS-Digital.ttf");
            return File.Exists(dsDigitalPath);
        }
        
        return false;
    }

    /// <summary>
    /// 获取字体文件下载提示
    /// </summary>
    public static string GetFontDownloadHint(string fontName)
    {
        if (fontName == "DS-Digital" && !IsFontFileAvailable(fontName))
        {
            return "提示：要获得最佳LED效果，请下载DS-Digital.ttf字体文件并放置在Fonts文件夹中。\n" +
                   "当前使用等宽字体作为替代。";
        }

        return string.Empty;
    }

    /// <summary>
    /// 从字体文件获取字体名称
    /// </summary>
    private static string GetFontNameFromFile(string fontFile)
    {
        try
        {
            // 这里可以使用更复杂的字体文件解析，现在简单使用文件名
            var fileName = Path.GetFileNameWithoutExtension(fontFile);

            // 处理常见的字体文件命名模式
            fileName = fileName.Replace("_", " ");
            fileName = fileName.Replace("-", " ");

            return fileName;
        }
        catch
        {
            return "";
        }
    }

    /// <summary>
    /// 获取字体类别
    /// </summary>
    private static string GetFontCategory(string fontName)
    {
        var lowerName = fontName.ToLower();

        // 数字/等宽字体
        if (IsMonospaceFont(fontName) || IsDigitalFont(fontName))
            return "等宽数字";

        // 中文字体
        if (lowerName.Contains("microsoft yahei") || lowerName.Contains("simsun") ||
            lowerName.Contains("simhei") || lowerName.Contains("kaiti") ||
            lowerName.Contains("fangsong") || lowerName.Contains("微软雅黑") ||
            lowerName.Contains("宋体") || lowerName.Contains("黑体") ||
            lowerName.Contains("楷体") || lowerName.Contains("仿宋"))
            return "中文字体";

        // 衬线字体
        if (lowerName.Contains("times") || lowerName.Contains("serif") ||
            lowerName.Contains("georgia") || lowerName.Contains("garamond"))
            return "衬线字体";

        // 无衬线字体
        if (lowerName.Contains("arial") || lowerName.Contains("helvetica") ||
            lowerName.Contains("calibri") || lowerName.Contains("segoe") ||
            lowerName.Contains("verdana") || lowerName.Contains("tahoma"))
            return "无衬线字体";

        // 装饰字体
        if (lowerName.Contains("comic") || lowerName.Contains("impact") ||
            lowerName.Contains("brush") || lowerName.Contains("script"))
            return "装饰字体";

        return "其他字体";
    }

    /// <summary>
    /// 检查是否为等宽字体
    /// </summary>
    private static bool IsMonospaceFont(string fontName)
    {
        var lowerName = fontName.ToLower();
        return lowerName.Contains("consolas") || lowerName.Contains("courier") ||
               lowerName.Contains("monaco") || lowerName.Contains("menlo") ||
               lowerName.Contains("source code") || lowerName.Contains("fira code") ||
               lowerName.Contains("inconsolata") || lowerName.Contains("dejavu sans mono");
    }

    /// <summary>
    /// 获取字体统计信息
    /// </summary>
    public static Dictionary<string, int> GetFontStatistics()
    {
        LoadAllFonts();

        var stats = new Dictionary<string, int>
        {
            ["系统字体总数"] = systemFonts.Count,
            ["自定义字体总数"] = customFonts.Count,
            ["等宽字体数量"] = systemFonts.Values.Count(f => f.IsMonospace),
            ["数字字体数量"] = systemFonts.Values.Count(f => f.IsDigital),
            ["中文字体数量"] = systemFonts.Values.Count(f => f.Category == "中文字体")
        };

        return stats;
    }

    /// <summary>
    /// 搜索字体
    /// </summary>
    public static List<string> SearchFonts(string keyword)
    {
        LoadAllFonts();

        if (string.IsNullOrWhiteSpace(keyword))
            return GetAvailableFonts();

        var lowerKeyword = keyword.ToLower();
        var results = new List<string>();

        // 搜索自定义字体
        results.AddRange(customFonts.Keys.Where(name =>
            name.ToLower().Contains(lowerKeyword)));

        // 搜索系统字体
        results.AddRange(systemFonts.Keys.Where(name =>
            name.ToLower().Contains(lowerKeyword) ||
            systemFonts[name].DisplayName.ToLower().Contains(lowerKeyword)));

        return results.Distinct().OrderBy(f => f).ToList();
    }
}
