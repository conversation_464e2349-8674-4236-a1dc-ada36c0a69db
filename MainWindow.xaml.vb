Imports System.Windows
Imports System.Windows.Threading
Imports System.Windows.Input
Imports System.ComponentModel
Imports System.Windows.Media

Namespace DigitalClock
    ''' <summary>
    ''' 主窗口类
    ''' </summary>
    Class MainWindow
        Inherits Window

        ' 时钟定时器
        Private WithEvents clockTimer As DispatcherTimer

        ' 设置窗口
        Private settingsWindow As SettingsWindow

        ' 托盘图标
        Private notifyIcon As System.Windows.Forms.NotifyIcon

        ' 热键ID
        Private Const HOTKEY_SHOW_HIDE As Integer = 1

        ''' <summary>
        ''' 构造函数
        ''' </summary>
        Public Sub New()
            InitializeComponent()
            InitializeClock()
            InitializeTrayIcon()
            ApplySettings()

            ' 窗口加载完成后启用Fluent Design效果
            AddHandler Me.Loaded, AddressOf OnWindowLoaded

            ' 简化版本：暂时禁用全局热键
            ' AddHandler Me.SourceInitialized, AddressOf OnSourceInitialized
        End Sub

        ''' <summary>
        ''' 初始化时钟
        ''' </summary>
        Private Sub InitializeClock()
            clockTimer = New DispatcherTimer()
            clockTimer.Interval = TimeSpan.FromSeconds(1)
            clockTimer.Start()
        End Sub

        ''' <summary>
        ''' 初始化托盘图标
        ''' </summary>
        Private Sub InitializeTrayIcon()
            ' 简化版本：暂时禁用托盘图标以避免依赖问题
            ' notifyIcon = New System.Windows.Forms.NotifyIcon()
            ' notifyIcon.Text = "极简数字时钟"
            ' notifyIcon.Visible = True
        End Sub

        ''' <summary>
        ''' 时钟定时器事件
        ''' </summary>
        Private Sub ClockTimer_Tick(sender As Object, e As EventArgs) Handles clockTimer.Tick
            UpdateTimeDisplay()
        End Sub

        ''' <summary>
        ''' 更新时间显示
        ''' </summary>
        Private Sub UpdateTimeDisplay()
            Dim now As DateTime = DateTime.Now

            ' 更新时间
            If SettingsManager.Is24HourFormat Then
                TimeTextBlock.Text = now.ToString("HH:mm:ss")
                StatusText.Text = "24H"
            Else
                TimeTextBlock.Text = now.ToString("hh:mm:ss tt")
                StatusText.Text = "12H"
            End If

            ' 更新日期
            DateTextBlock.Text = now.ToString("yyyy年MM月dd日 dddd")
        End Sub

        ''' <summary>
        ''' 窗口源初始化事件
        ''' </summary>
        Protected Overrides Sub OnSourceInitialized(e As EventArgs)
            ' 注册全局热键
            SystemIntegrationHelper.RegisterGlobalHotKey(Me, HOTKEY_SHOW_HIDE, ModifierKeys.Control Or ModifierKeys.Alt, Key.T)

            ' 调用基类方法
            MyBase.OnSourceInitialized(e)

            ' 添加窗口消息处理
            Dim source As System.Windows.Interop.HwndSource = System.Windows.Interop.HwndSource.FromHwnd(New System.Windows.Interop.WindowInteropHelper(Me).Handle)
            source.AddHook(AddressOf WndProc)
        End Sub

        ''' <summary>
        ''' 窗口加载完成事件
        ''' </summary>
        Private Sub OnWindowLoaded(sender As Object, e As RoutedEventArgs)
            ' 启用Fluent Design亚克力效果
            If FluentDesignHelper.IsAcrylicSupported() Then
                Dim tintColor As Color = If(FluentDesignHelper.IsDarkTheme(), Colors.Black, Colors.White)
                FluentDesignHelper.EnableAcrylicEffect(Me, tintColor, 0.7)
            End If

            ' 适应系统主题
            AdaptToSystemTheme()
        End Sub

        ''' <summary>
        ''' 窗口消息处理
        ''' </summary>
        Private Function WndProc(hwnd As IntPtr, msg As Integer, wParam As IntPtr, lParam As IntPtr, ByRef handled As Boolean) As IntPtr
            Const WM_HOTKEY As Integer = &H312

            If msg = WM_HOTKEY Then
                Dim hotkeyId As Integer = wParam.ToInt32()
                If hotkeyId = HOTKEY_SHOW_HIDE Then
                    ' 处理显示/隐藏热键
                    If Me.IsVisible Then
                        Me.Hide()
                    Else
                        ShowFromTray(Nothing, Nothing)
                    End If
                    handled = True
                End If
            End If

            Return IntPtr.Zero
        End Function

        ''' <summary>
        ''' 适应系统主题
        ''' </summary>
        Private Sub AdaptToSystemTheme()
            If FluentDesignHelper.IsDarkTheme() Then
                ' 深色主题
                MainBorder.Background = New SolidColorBrush(Color.FromArgb(180, 30, 30, 30))
                MainBorder.BorderBrush = New SolidColorBrush(Color.FromArgb(100, 255, 255, 255))
            Else
                ' 浅色主题
                MainBorder.Background = New SolidColorBrush(Color.FromArgb(180, 240, 240, 240))
                MainBorder.BorderBrush = New SolidColorBrush(Color.FromArgb(100, 0, 0, 0))
            End If
        End Sub

        ''' <summary>
        ''' 应用设置
        ''' </summary>
        Private Sub ApplySettings()
            ' 应用字体设置
            TimeTextBlock.FontFamily = New FontFamily(SettingsManager.FontFamily)
            TimeTextBlock.FontSize = SettingsManager.FontSize

            ' 应用颜色设置
            TimeTextBlock.Foreground = New SolidColorBrush(SettingsManager.FontColor)
            DateTextBlock.Foreground = New SolidColorBrush(SettingsManager.FontColor)

            ' 应用透明度设置
            Me.Opacity = SettingsManager.WindowOpacity

            ' 应用置顶设置
            Me.Topmost = SettingsManager.IsTopMost
            UpdateTopMostUI()

            ' 更新时间显示
            UpdateTimeDisplay()

            ' 重新适应系统主题
            AdaptToSystemTheme()
        End Sub

        ''' <summary>
        ''' 更新置顶UI状态
        ''' </summary>
        Private Sub UpdateTopMostUI()
            If Me.Topmost Then
                TopMostButton.Content = "取消置顶"
                TopMostMenuItem.Header = "取消置顶"
                TopMostIndicator.Visibility = Visibility.Visible
            Else
                TopMostButton.Content = "置顶"
                TopMostMenuItem.Header = "置顶"
                TopMostIndicator.Visibility = Visibility.Collapsed
            End If
        End Sub

        ''' <summary>
        ''' 窗口拖动事件
        ''' </summary>
        Private Sub Border_MouseLeftButtonDown(sender As Object, e As MouseButtonEventArgs)
            If e.ButtonState = MouseButtonState.Pressed Then
                Me.DragMove()
            End If
        End Sub

        ''' <summary>
        ''' 设置按钮点击事件
        ''' </summary>
        Private Sub SettingsButton_Click(sender As Object, e As RoutedEventArgs)
            ShowSettings()
        End Sub

        ''' <summary>
        ''' 显示设置窗口
        ''' </summary>
        Private Sub ShowSettings()
            If settingsWindow Is Nothing OrElse Not settingsWindow.IsLoaded Then
                settingsWindow = New SettingsWindow()
                AddHandler settingsWindow.SettingsChanged, AddressOf OnSettingsChanged
            End If
            settingsWindow.Show()
            settingsWindow.Activate()
        End Sub

        ''' <summary>
        ''' 设置更改事件处理
        ''' </summary>
        Private Sub OnSettingsChanged()
            ApplySettings()
        End Sub

        ''' <summary>
        ''' 置顶按钮点击事件
        ''' </summary>
        Private Sub TopMostButton_Click(sender As Object, e As RoutedEventArgs)
            ToggleTopMost()
        End Sub

        ''' <summary>
        ''' 切换置顶状态
        ''' </summary>
        Private Sub ToggleTopMost()
            Me.Topmost = Not Me.Topmost
            SettingsManager.IsTopMost = Me.Topmost
            UpdateTopMostUI()
        End Sub

        ''' <summary>
        ''' 最小化按钮点击事件
        ''' </summary>
        Private Sub MinimizeButton_Click(sender As Object, e As RoutedEventArgs)
            Me.Hide()
        End Sub

        ''' <summary>
        ''' 关闭按钮点击事件
        ''' </summary>
        Private Sub CloseButton_Click(sender As Object, e As RoutedEventArgs)
            Me.Hide()
        End Sub

        ' 右键菜单事件处理
        Private Sub SettingsMenuItem_Click(sender As Object, e As RoutedEventArgs)
            ShowSettings()
        End Sub

        Private Sub TopMostMenuItem_Click(sender As Object, e As RoutedEventArgs)
            ToggleTopMost()
        End Sub

        Private Sub Hour12MenuItem_Click(sender As Object, e As RoutedEventArgs)
            SettingsManager.Is24HourFormat = False
            UpdateTimeDisplay()
        End Sub

        Private Sub Hour24MenuItem_Click(sender As Object, e As RoutedEventArgs)
            SettingsManager.Is24HourFormat = True
            UpdateTimeDisplay()
        End Sub

        Private Sub AutoStartMenuItem_Click(sender As Object, e As RoutedEventArgs)
            Try
                Dim isEnabled As Boolean = SystemIntegrationHelper.IsAutoStartEnabled()
                SystemIntegrationHelper.SetAutoStart(Not isEnabled)

                ' 更新菜单项文本
                AutoStartMenuItem.Header = If(Not isEnabled, "取消开机自启", "开机自启")

                ' 更新设置
                SettingsManager.AutoStart = Not isEnabled
                SettingsManager.SaveSettings()

                MessageBox.Show(If(Not isEnabled, "已启用开机自启", "已取消开机自启"), "设置", MessageBoxButton.OK, MessageBoxImage.Information)

            Catch ex As Exception
                MessageBox.Show($"设置开机自启失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error)
            End Try
        End Sub

        Private Sub AboutMenuItem_Click(sender As Object, e As RoutedEventArgs)
            MessageBox.Show("极简数字时钟 v1.0" & vbCrLf & "Windows 11 现代化数字时钟应用", "关于", MessageBoxButton.OK, MessageBoxImage.Information)
        End Sub

        Private Sub ExitMenuItem_Click(sender As Object, e As RoutedEventArgs)
            ExitApplication()
        End Sub

        ''' <summary>
        ''' 从托盘显示窗口
        ''' </summary>
        Private Sub ShowFromTray(sender As Object, e As EventArgs)
            Me.Show()
            Me.WindowState = WindowState.Normal
            Me.Activate()
        End Sub

        ''' <summary>
        ''' 退出应用程序
        ''' </summary>
        Private Sub ExitApplication(Optional sender As Object = Nothing, Optional e As EventArgs = Nothing)
            notifyIcon.Visible = False
            notifyIcon.Dispose()
            Application.Current.Shutdown()
        End Sub

        ''' <summary>
        ''' 窗口关闭事件
        ''' </summary>
        Protected Overrides Sub OnClosing(e As CancelEventArgs)
            e.Cancel = True
            Me.Hide()
        End Sub

        ''' <summary>
        ''' 窗口状态改变事件
        ''' </summary>
        Protected Overrides Sub OnStateChanged(e As EventArgs)
            If Me.WindowState = WindowState.Minimized Then
                Me.Hide()
            End If
            MyBase.OnStateChanged(e)
        End Sub
    End Class
End Namespace
