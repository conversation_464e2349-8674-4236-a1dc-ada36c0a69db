using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Shapes;

namespace WorkingClock;

/// <summary>
/// 图标管理器 - 提供现代化的矢量图标
/// </summary>
public static class IconManager
{
    /// <summary>
    /// 图标类型枚举
    /// </summary>
    public enum IconType
    {
        Settings,
        Weather,
        Refresh,
        Pin,
        Clock,
        Font,
        Color,
        Size,
        Shadow,
        Border,
        Background,
        Position,
        About,
        Exit,
        Play,
        Pause,
        Stop,
        Minimize,
        Maximize,
        Close,
        Search,
        Download,
        Upload,
        Save,
        Load,
        Copy,
        Paste,
        Cut,
        Undo,
        Redo
    }

    /// <summary>
    /// 获取简化的图标路径数据
    /// </summary>
    public static string GetIconPath(IconType iconType)
    {
        return iconType switch
        {
            IconType.Settings => "M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z",
            IconType.Weather => "M12,3L13,6L16,5L15,8L18,9L16,11L18,13L15,14L16,17L13,16L12,19L11,16L8,17L9,14L6,13L8,11L6,9L9,8L8,5L11,6L12,3Z",
            IconType.Refresh => "M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12H18A6,6 0 0,1 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6V8L16,4L12,0V2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12H20A8,8 0 0,1 12,4Z",
            IconType.Pin => "M16,12V4H17V2H7V4H8V12L6,14V16H11V22H13V16H18V14L16,12Z",
            IconType.Clock => "M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M16,16L11,13V7H13V12L17,15L16,16Z",
            IconType.About => "M11,9H13V7H11M12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,17H13V11H11V17Z",
            IconType.Exit => "M16,17V14H9V10H16V7L21,12L16,17M14,2A2,2 0 0,1 16,4V6H14V4H5V20H14V18H16V20A2,2 0 0,1 14,22H5A2,2 0 0,1 3,20V4A2,2 0 0,1 5,2H14Z",
            _ => "M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2Z"
        };
    }

    /// <summary>
    /// 创建图标控件
    /// </summary>
    public static Path CreateIcon(IconType iconType, double size = 16, Brush? fill = null)
    {
        var icon = new Path
        {
            Data = Geometry.Parse(GetIconPath(iconType)),
            Fill = fill ?? new SolidColorBrush(Colors.White),
            Width = size,
            Height = size,
            Stretch = Stretch.Uniform,
            HorizontalAlignment = HorizontalAlignment.Center,
            VerticalAlignment = VerticalAlignment.Center
        };

        return icon;
    }

    /// <summary>
    /// 创建带图标的按钮
    /// </summary>
    public static Button CreateIconButton(IconType iconType, string text = "", double iconSize = 16)
    {
        var button = new Button
        {
            Style = CreateModernButtonStyle(),
            Padding = new Thickness(12, 8, 12, 8),
            Margin = new Thickness(4, 4, 4, 4)
        };

        var stackPanel = new StackPanel
        {
            Orientation = Orientation.Horizontal,
            VerticalAlignment = VerticalAlignment.Center
        };

        // 添加图标
        var icon = CreateIcon(iconType, iconSize);
        stackPanel.Children.Add(icon);

        // 添加文本（如果有）
        if (!string.IsNullOrEmpty(text))
        {
            var textBlock = new TextBlock
            {
                Text = text,
                Margin = new Thickness(8, 0, 0, 0),
                VerticalAlignment = VerticalAlignment.Center
            };
            stackPanel.Children.Add(textBlock);
        }

        button.Content = stackPanel;
        return button;
    }

    /// <summary>
    /// 创建现代化按钮样式
    /// </summary>
    public static Style CreateModernButtonStyle()
    {
        var style = new Style(typeof(Button));
        
        // 设置基本属性
        style.Setters.Add(new Setter(Control.BackgroundProperty, new SolidColorBrush(Color.FromArgb(40, 255, 255, 255))));
        style.Setters.Add(new Setter(Control.ForegroundProperty, new SolidColorBrush(Colors.White)));
        style.Setters.Add(new Setter(Control.BorderBrushProperty, new SolidColorBrush(Color.FromArgb(60, 255, 255, 255))));
        style.Setters.Add(new Setter(Control.BorderThicknessProperty, new Thickness(1)));
        style.Setters.Add(new Setter(Control.PaddingProperty, new Thickness(12, 8, 12, 8)));
        style.Setters.Add(new Setter(Control.MarginProperty, new Thickness(4, 4, 4, 4)));
        style.Setters.Add(new Setter(Control.FontSizeProperty, 12.0));
        style.Setters.Add(new Setter(Control.CursorProperty, System.Windows.Input.Cursors.Hand));

        // 设置模板
        var template = new ControlTemplate(typeof(Button));
        
        var border = new FrameworkElementFactory(typeof(Border));
        border.Name = "border";
        border.SetBinding(Border.BackgroundProperty, new System.Windows.Data.Binding("Background") { RelativeSource = new System.Windows.Data.RelativeSource(System.Windows.Data.RelativeSourceMode.TemplatedParent) });
        border.SetBinding(Border.BorderBrushProperty, new System.Windows.Data.Binding("BorderBrush") { RelativeSource = new System.Windows.Data.RelativeSource(System.Windows.Data.RelativeSourceMode.TemplatedParent) });
        border.SetBinding(Border.BorderThicknessProperty, new System.Windows.Data.Binding("BorderThickness") { RelativeSource = new System.Windows.Data.RelativeSource(System.Windows.Data.RelativeSourceMode.TemplatedParent) });
        border.SetValue(Border.CornerRadiusProperty, new CornerRadius(6));

        var contentPresenter = new FrameworkElementFactory(typeof(ContentPresenter));
        contentPresenter.SetValue(ContentPresenter.HorizontalAlignmentProperty, HorizontalAlignment.Center);
        contentPresenter.SetValue(ContentPresenter.VerticalAlignmentProperty, VerticalAlignment.Center);
        contentPresenter.SetBinding(ContentPresenter.MarginProperty, new System.Windows.Data.Binding("Padding") { RelativeSource = new System.Windows.Data.RelativeSource(System.Windows.Data.RelativeSourceMode.TemplatedParent) });

        border.AppendChild(contentPresenter);
        template.VisualTree = border;

        style.Setters.Add(new Setter(Control.TemplateProperty, template));

        return style;
    }

    /// <summary>
    /// 获取天气图标
    /// </summary>
    public static string GetWeatherIcon(int weatherCode)
    {
        return weatherCode switch
        {
            0 => "☀️", // 晴朗
            1 => "🌤️", // 基本晴朗
            2 => "⛅", // 部分多云
            3 => "☁️", // 阴天
            45 or 48 => "🌫️", // 雾
            51 or 53 or 55 or 61 or 63 or 65 => "🌧️", // 雨
            56 or 57 or 66 or 67 => "🌨️", // 冻雨
            71 or 73 or 75 or 77 => "❄️", // 雪
            80 or 81 or 82 => "🌦️", // 阵雨
            85 or 86 => "🌨️", // 阵雪
            95 or 96 or 99 => "⛈️", // 雷暴
            _ => "🌡️" // 未知
        };
    }

    /// <summary>
    /// 创建现代化的菜单项
    /// </summary>
    public static System.Windows.Controls.MenuItem CreateModernMenuItem(IconType iconType, string header, System.Windows.Input.ICommand? command = null)
    {
        var menuItem = new System.Windows.Controls.MenuItem
        {
            Header = header,
            Command = command
        };

        // 创建图标
        var icon = CreateIcon(iconType, 16, new SolidColorBrush(Colors.White));
        menuItem.Icon = icon;

        return menuItem;
    }
}
