========================================
    极简数字时钟 v2.0 - 独立EXE版本
========================================

🎉 恭喜！您已成功获得独立运行的数字时钟程序！

📁 文件说明：
• WorkingClock.exe - 主程序文件（约147MB）
• WorkingClock.pdb - 调试信息文件（可删除）
• 使用说明.txt - 本文件

🚀 使用方法：
1. 直接双击 WorkingClock.exe 即可运行
2. 无需安装任何运行时或依赖
3. 可在任何 Windows 10/11 电脑上运行
4. 可以复制到U盘或其他电脑使用

✨ 功能特色：
• 🕐 实时时间显示
• 🌤️ 免费天气预报（自动定位）
• ⚙️ 完全个性化设置
• 📌 窗口置顶功能
• 🖱️ 拖动移动窗口
• 🎨 半透明现代化界面

💡 操作提示：
• 右键点击时钟可访问所有功能
• 天气信息每30分钟自动更新
• 支持显示/隐藏天气信息
• 可手动刷新天气数据
• 支持12/24小时制切换

🌐 天气数据来源：
• Open-Meteo API（开源免费）
• IP-API 地理定位服务
• 无需API Key，完全免费

🔧 个性化设置：
• 边框样式：圆角、直角等
• 背景透明度：10%-100%
• 字体设置：7种字体，20-100px大小
• 颜色方案：8种预设颜色
• 阴影效果：可调强度和模糊
• 窗口大小：4种预设尺寸
• 窗口位置：5种预设位置

📊 系统要求：
• Windows 10 版本 1607 或更高版本
• Windows 11（推荐）
• 约 200MB 可用磁盘空间
• 网络连接（用于天气功能）

🛡️ 隐私保护：
• 仅用于获取天气数据
• 不存储个人信息
• 不上传用户数据
• 使用HTTPS安全连接

💾 便携使用：
• 可以复制到任何位置运行
• 支持U盘便携使用
• 设置会保存在用户目录
• 无需安装，绿色软件

🔍 故障排除：
• 如果天气显示"获取失败"，请检查网络连接
• 右键选择"刷新天气"可手动更新
• 如果程序无法启动，请确保系统为Windows 10+

📞 技术支持：
• 这是一个开源项目
• 基于 .NET 6 和 WPF 技术
• 使用免费的天气API服务

🎯 版本信息：
• 版本：2.0
• 编译日期：2024年
• 架构：x64
• 类型：单文件独立部署

========================================
享受您的智能数字时钟体验！🕐🌤️✨
========================================
