﻿#pragma checksum "..\..\..\SettingsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "307C0DEA7B629DFB08030D8333C398A00A1CD8AB"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace WorkingClock {
    
    
    /// <summary>
    /// SettingsWindow
    /// </summary>
    public partial class SettingsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 38 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox BorderStyleComboBox;
        
        #line default
        #line hidden
        
        
        #line 47 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox BorderColorComboBox;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider BackgroundOpacitySlider;
        
        #line default
        #line hidden
        
        
        #line 66 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BackgroundOpacityText;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox BackgroundColorComboBox;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FontFamilyComboBox;
        
        #line default
        #line hidden
        
        
        #line 105 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider FontSizeSlider;
        
        #line default
        #line hidden
        
        
        #line 111 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FontSizeText;
        
        #line default
        #line hidden
        
        
        #line 120 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FontColorComboBox;
        
        #line default
        #line hidden
        
        
        #line 133 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FontWeightComboBox;
        
        #line default
        #line hidden
        
        
        #line 149 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider ShadowOpacitySlider;
        
        #line default
        #line hidden
        
        
        #line 155 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ShadowOpacityText;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider ShadowBlurSlider;
        
        #line default
        #line hidden
        
        
        #line 171 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ShadowBlurText;
        
        #line default
        #line hidden
        
        
        #line 186 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox WindowSizeComboBox;
        
        #line default
        #line hidden
        
        
        #line 195 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox WindowPositionComboBox;
        
        #line default
        #line hidden
        
        
        #line 207 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border PreviewBorder;
        
        #line default
        #line hidden
        
        
        #line 213 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PreviewTextBlock;
        
        #line default
        #line hidden
        
        
        #line 222 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Media.Effects.DropShadowEffect PreviewShadow;
        
        #line default
        #line hidden
        
        
        #line 241 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetButton;
        
        #line default
        #line hidden
        
        
        #line 248 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ApplyButton;
        
        #line default
        #line hidden
        
        
        #line 255 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OkButton;
        
        #line default
        #line hidden
        
        
        #line 262 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/WorkingClock;component/settingswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\SettingsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.BorderStyleComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 38 "..\..\..\SettingsWindow.xaml"
            this.BorderStyleComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.BorderStyleComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.BorderColorComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 47 "..\..\..\SettingsWindow.xaml"
            this.BorderColorComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.BorderColorComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.BackgroundOpacitySlider = ((System.Windows.Controls.Slider)(target));
            
            #line 65 "..\..\..\SettingsWindow.xaml"
            this.BackgroundOpacitySlider.ValueChanged += new System.Windows.RoutedPropertyChangedEventHandler<double>(this.BackgroundOpacitySlider_ValueChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            this.BackgroundOpacityText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.BackgroundColorComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 75 "..\..\..\SettingsWindow.xaml"
            this.BackgroundColorComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.BackgroundColorComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.FontFamilyComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 92 "..\..\..\SettingsWindow.xaml"
            this.FontFamilyComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.FontFamilyComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.FontSizeSlider = ((System.Windows.Controls.Slider)(target));
            
            #line 110 "..\..\..\SettingsWindow.xaml"
            this.FontSizeSlider.ValueChanged += new System.Windows.RoutedPropertyChangedEventHandler<double>(this.FontSizeSlider_ValueChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            this.FontSizeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.FontColorComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 120 "..\..\..\SettingsWindow.xaml"
            this.FontColorComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.FontColorComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 10:
            this.FontWeightComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 133 "..\..\..\SettingsWindow.xaml"
            this.FontWeightComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.FontWeightComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 11:
            this.ShadowOpacitySlider = ((System.Windows.Controls.Slider)(target));
            
            #line 154 "..\..\..\SettingsWindow.xaml"
            this.ShadowOpacitySlider.ValueChanged += new System.Windows.RoutedPropertyChangedEventHandler<double>(this.ShadowOpacitySlider_ValueChanged);
            
            #line default
            #line hidden
            return;
            case 12:
            this.ShadowOpacityText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.ShadowBlurSlider = ((System.Windows.Controls.Slider)(target));
            
            #line 170 "..\..\..\SettingsWindow.xaml"
            this.ShadowBlurSlider.ValueChanged += new System.Windows.RoutedPropertyChangedEventHandler<double>(this.ShadowBlurSlider_ValueChanged);
            
            #line default
            #line hidden
            return;
            case 14:
            this.ShadowBlurText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.WindowSizeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 186 "..\..\..\SettingsWindow.xaml"
            this.WindowSizeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.WindowSizeComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 16:
            this.WindowPositionComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 195 "..\..\..\SettingsWindow.xaml"
            this.WindowPositionComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.WindowPositionComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 17:
            this.PreviewBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 18:
            this.PreviewTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.PreviewShadow = ((System.Windows.Media.Effects.DropShadowEffect)(target));
            return;
            case 20:
            this.ResetButton = ((System.Windows.Controls.Button)(target));
            
            #line 246 "..\..\..\SettingsWindow.xaml"
            this.ResetButton.Click += new System.Windows.RoutedEventHandler(this.ResetButton_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.ApplyButton = ((System.Windows.Controls.Button)(target));
            
            #line 253 "..\..\..\SettingsWindow.xaml"
            this.ApplyButton.Click += new System.Windows.RoutedEventHandler(this.ApplyButton_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.OkButton = ((System.Windows.Controls.Button)(target));
            
            #line 260 "..\..\..\SettingsWindow.xaml"
            this.OkButton.Click += new System.Windows.RoutedEventHandler(this.OkButton_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 266 "..\..\..\SettingsWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

