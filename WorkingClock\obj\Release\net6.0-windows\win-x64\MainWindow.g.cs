﻿#pragma checksum "..\..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "947611FC6EC2FEC0D3552A556E8531E1D7AD0BF1"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using WorkingClock;


namespace WorkingClock {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 20 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border MainBorder;
        
        #line default
        #line hidden
        
        
        #line 53 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TimeTextBlock;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DateTextBlock;
        
        #line default
        #line hidden
        
        
        #line 105 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel WeatherPanel;
        
        #line default
        #line hidden
        
        
        #line 112 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WeatherIconTextBlock;
        
        #line default
        #line hidden
        
        
        #line 124 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TemperatureTextBlock;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WeatherDescriptionTextBlock;
        
        #line default
        #line hidden
        
        
        #line 153 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LocationTextBlock;
        
        #line default
        #line hidden
        
        
        #line 176 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 187 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ContextMenu MainContextMenu;
        
        #line default
        #line hidden
        
        
        #line 210 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.MenuItem SettingsMenuItem;
        
        #line default
        #line hidden
        
        
        #line 211 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.MenuItem AnimationMenuItem;
        
        #line default
        #line hidden
        
        
        #line 212 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.MenuItem HTCSenseModeMenuItem;
        
        #line default
        #line hidden
        
        
        #line 214 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.MenuItem ShowWeatherMenuItem;
        
        #line default
        #line hidden
        
        
        #line 215 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.MenuItem RefreshWeatherMenuItem;
        
        #line default
        #line hidden
        
        
        #line 217 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.MenuItem TopMostMenuItem;
        
        #line default
        #line hidden
        
        
        #line 219 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.MenuItem Hour12MenuItem;
        
        #line default
        #line hidden
        
        
        #line 220 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.MenuItem Hour24MenuItem;
        
        #line default
        #line hidden
        
        
        #line 222 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.MenuItem AboutMenuItem;
        
        #line default
        #line hidden
        
        
        #line 223 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.MenuItem ExitMenuItem;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/WorkingClock;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.MainBorder = ((System.Windows.Controls.Border)(target));
            
            #line 24 "..\..\..\..\MainWindow.xaml"
            this.MainBorder.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Border_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 2:
            this.TimeTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.DateTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.WeatherPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 5:
            this.WeatherIconTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.TemperatureTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.WeatherDescriptionTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.LocationTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.MainContextMenu = ((System.Windows.Controls.ContextMenu)(target));
            return;
            case 11:
            this.SettingsMenuItem = ((System.Windows.Controls.MenuItem)(target));
            
            #line 210 "..\..\..\..\MainWindow.xaml"
            this.SettingsMenuItem.Click += new System.Windows.RoutedEventHandler(this.SettingsMenuItem_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.AnimationMenuItem = ((System.Windows.Controls.MenuItem)(target));
            
            #line 211 "..\..\..\..\MainWindow.xaml"
            this.AnimationMenuItem.Click += new System.Windows.RoutedEventHandler(this.AnimationMenuItem_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.HTCSenseModeMenuItem = ((System.Windows.Controls.MenuItem)(target));
            
            #line 212 "..\..\..\..\MainWindow.xaml"
            this.HTCSenseModeMenuItem.Click += new System.Windows.RoutedEventHandler(this.HTCSenseModeMenuItem_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.ShowWeatherMenuItem = ((System.Windows.Controls.MenuItem)(target));
            
            #line 214 "..\..\..\..\MainWindow.xaml"
            this.ShowWeatherMenuItem.Click += new System.Windows.RoutedEventHandler(this.ShowWeatherMenuItem_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.RefreshWeatherMenuItem = ((System.Windows.Controls.MenuItem)(target));
            
            #line 215 "..\..\..\..\MainWindow.xaml"
            this.RefreshWeatherMenuItem.Click += new System.Windows.RoutedEventHandler(this.RefreshWeatherMenuItem_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.TopMostMenuItem = ((System.Windows.Controls.MenuItem)(target));
            
            #line 217 "..\..\..\..\MainWindow.xaml"
            this.TopMostMenuItem.Click += new System.Windows.RoutedEventHandler(this.TopMostMenuItem_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.Hour12MenuItem = ((System.Windows.Controls.MenuItem)(target));
            
            #line 219 "..\..\..\..\MainWindow.xaml"
            this.Hour12MenuItem.Click += new System.Windows.RoutedEventHandler(this.Hour12MenuItem_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.Hour24MenuItem = ((System.Windows.Controls.MenuItem)(target));
            
            #line 220 "..\..\..\..\MainWindow.xaml"
            this.Hour24MenuItem.Click += new System.Windows.RoutedEventHandler(this.Hour24MenuItem_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.AboutMenuItem = ((System.Windows.Controls.MenuItem)(target));
            
            #line 222 "..\..\..\..\MainWindow.xaml"
            this.AboutMenuItem.Click += new System.Windows.RoutedEventHandler(this.AboutMenuItem_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.ExitMenuItem = ((System.Windows.Controls.MenuItem)(target));
            
            #line 223 "..\..\..\..\MainWindow.xaml"
            this.ExitMenuItem.Click += new System.Windows.RoutedEventHandler(this.ExitMenuItem_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

