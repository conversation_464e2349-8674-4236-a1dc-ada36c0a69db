Imports System.Windows
Imports System.Diagnostics
Imports System.Runtime.InteropServices

Namespace DigitalClock
    ''' <summary>
    ''' 应用程序主类
    ''' </summary>
    Partial Class App
        Inherits Application

    ''' <summary>
    ''' 应用程序启动事件
    ''' </summary>
    Private Sub Application_Startup(sender As Object, e As StartupEventArgs) Handles Me.Startup
        ' 确保只有一个实例运行
        Dim currentProcess As Process = Process.GetCurrentProcess()
        Dim processes() As Process = Process.GetProcessesByName(currentProcess.ProcessName)
        
        If processes.Length > 1 Then
            ' 如果已有实例运行，激活现有窗口
            For Each proc As Process In processes
                If proc.Id <> currentProcess.Id Then
                    ShowWindow(proc.MainWindowHandle, SW_RESTORE)
                    SetForegroundWindow(proc.MainWindowHandle)
                    Exit For
                End If
            Next
            ' 退出当前实例
            Me.Shutdown()
            Return
        End If

        ' 初始化应用程序设置
        SettingsManager.LoadSettings()
        
        ' 创建主窗口
        Dim mainWindow As New MainWindow()
        mainWindow.Show()
    End Sub

    ''' <summary>
    ''' 应用程序退出事件
    ''' </summary>
    Private Sub Application_Exit(sender As Object, e As ExitEventArgs) Handles Me.Exit
        ' 保存设置
        SettingsManager.SaveSettings()
    End Sub

    ' Windows API 声明
    <DllImport("user32.dll")>
    Private Shared Function ShowWindow(hWnd As IntPtr, nCmdShow As Integer) As Boolean
    End Function

    <DllImport("user32.dll")>
    Private Shared Function SetForegroundWindow(hWnd As IntPtr) As Boolean
    End Function

    Private Const SW_RESTORE As Integer = 9
    End Class
End Namespace
