using System;
using System.Windows;
using System.Windows.Input;
using System.Windows.Threading;

namespace SimpleClock
{
    public partial class MainWindow : Window
    {
        private DispatcherTimer clockTimer;
        private bool is24HourFormat = true;

        public MainWindow()
        {
            InitializeComponent();
            InitializeClock();
        }

        private void InitializeClock()
        {
            clockTimer = new DispatcherTimer();
            clockTimer.Interval = TimeSpan.FromSeconds(1);
            clockTimer.Tick += ClockTimer_Tick;
            clockTimer.Start();
            UpdateTimeDisplay();
        }

        private void ClockTimer_Tick(object sender, EventArgs e)
        {
            UpdateTimeDisplay();
        }

        private void UpdateTimeDisplay()
        {
            var now = DateTime.Now;
            
            // 更新时间
            if (is24HourFormat)
            {
                TimeTextBlock.Text = now.ToString("HH:mm:ss");
                StatusText.Text = "24H";
            }
            else
            {
                TimeTextBlock.Text = now.ToString("hh:mm:ss tt");
                StatusText.Text = "12H";
            }
            
            // 更新日期
            DateTextBlock.Text = now.ToString("yyyy年MM月dd日 dddd");
        }

        private void Border_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ButtonState == MouseButtonState.Pressed)
            {
                this.DragMove();
            }
        }

        private void TopMostMenuItem_Click(object sender, RoutedEventArgs e)
        {
            this.Topmost = !this.Topmost;
            TopMostMenuItem.Header = this.Topmost ? "取消置顶" : "置顶";
        }

        private void Hour12MenuItem_Click(object sender, RoutedEventArgs e)
        {
            is24HourFormat = false;
            UpdateTimeDisplay();
        }

        private void Hour24MenuItem_Click(object sender, RoutedEventArgs e)
        {
            is24HourFormat = true;
            UpdateTimeDisplay();
        }

        private void AboutMenuItem_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("极简数字时钟 v1.0\nWindows 11 现代化数字时钟应用", "关于", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ExitMenuItem_Click(object sender, RoutedEventArgs e)
        {
            Application.Current.Shutdown();
        }
    }
}
