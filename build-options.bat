@echo off
echo ========================================
echo    极简数字时钟 - 编译选项
echo ========================================
echo.

REM 检查是否安装了.NET 6 SDK
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到 .NET 6 SDK
    echo 请从 https://dotnet.microsoft.com/download 下载并安装 .NET 6 SDK
    pause
    exit /b 1
)

if not exist "WorkingClock" (
    echo 错误: WorkingClock 项目不存在
    echo 请先运行 create-simple-clock.bat 创建项目
    pause
    exit /b 1
)

echo 请选择编译方式：
echo.
echo [1] 单文件EXE (推荐) - 所有文件打包成一个EXE，约80-120MB
echo [2] 框架依赖版本 - 需要安装.NET 6 Runtime，约1-2MB
echo [3] 独立部署版本 - 包含运行时，多个文件，约100MB
echo [4] 修剪版本 - 单文件+代码修剪，约60-80MB (可能不稳定)
echo.
set /p choice="请选择 (1-4): "

cd WorkingClock

echo.
echo 🔧 正在清理之前的构建...
dotnet clean --configuration Release

echo.

if "%choice%"=="1" (
    echo 📦 正在编译单文件EXE版本...
    echo 特点: 一个文件包含所有内容，可在任何Windows 10/11上运行
    echo.
    dotnet publish --configuration Release --runtime win-x64 --self-contained true --output "../DigitalClock-SingleFile" /p:PublishSingleFile=true /p:IncludeNativeLibrariesForSelfExtract=true /p:PublishTrimmed=false
    set "outputDir=DigitalClock-SingleFile"
    set "exeName=WorkingClock.exe"
    
) else if "%choice%"=="2" (
    echo 📦 正在编译框架依赖版本...
    echo 特点: 文件小，但需要目标电脑安装.NET 6 Runtime
    echo.
    dotnet publish --configuration Release --runtime win-x64 --self-contained false --output "../DigitalClock-FrameworkDependent"
    set "outputDir=DigitalClock-FrameworkDependent"
    set "exeName=WorkingClock.exe"
    
) else if "%choice%"=="3" (
    echo 📦 正在编译独立部署版本...
    echo 特点: 多个文件，包含完整运行时，可在任何Windows 10/11上运行
    echo.
    dotnet publish --configuration Release --runtime win-x64 --self-contained true --output "../DigitalClock-SelfContained"
    set "outputDir=DigitalClock-SelfContained"
    set "exeName=WorkingClock.exe"
    
) else if "%choice%"=="4" (
    echo 📦 正在编译修剪版本...
    echo 特点: 单文件+代码修剪，文件最小，但可能不稳定
    echo.
    dotnet publish --configuration Release --runtime win-x64 --self-contained true --output "../DigitalClock-Trimmed" /p:PublishSingleFile=true /p:IncludeNativeLibrariesForSelfExtract=true /p:PublishTrimmed=true
    set "outputDir=DigitalClock-Trimmed"
    set "exeName=WorkingClock.exe"
    
) else (
    echo 无效选择，退出...
    cd ..
    pause
    exit /b 1
)

cd ..

if %errorlevel% neq 0 (
    echo.
    echo ❌ 编译失败！请检查错误信息。
    pause
    exit /b 1
)

echo.
echo ✅ 编译成功！
echo.
echo 📁 输出目录: %outputDir%\
echo 📄 主程序: %outputDir%\%exeName%
echo.

if exist "%outputDir%\%exeName%" (
    echo 📊 文件信息:
    for %%I in ("%outputDir%\%exeName%") do (
        set /a sizeKB=%%~zI/1024
        set /a sizeMB=%%~zI/1048576
        echo    文件大小: %%~zI 字节 (约 !sizeMB! MB)
    )
    
    echo.
    echo 📂 输出目录内容:
    dir "%outputDir%" /b
    
    echo.
    echo 🚀 使用说明:
    if "%choice%"=="1" (
        echo • 直接双击 %exeName% 即可运行
        echo • 可以复制到任何 Windows 10/11 电脑上使用
        echo • 无需安装任何运行时
    ) else if "%choice%"=="2" (
        echo • 需要目标电脑安装 .NET 6 Runtime
        echo • 文件小，适合已安装.NET的环境
        echo • 双击 %exeName% 运行
    ) else if "%choice%"=="3" (
        echo • 需要复制整个文件夹到目标电脑
        echo • 无需安装任何运行时
        echo • 双击 %exeName% 运行
    ) else if "%choice%"=="4" (
        echo • 文件最小，但可能存在兼容性问题
        echo • 如果运行出错，请使用选项1
        echo • 双击 %exeName% 运行
    )
    
    echo.
    set /p runNow="是否现在运行编译好的程序？(y/n): "
    if /i "%runNow%"=="y" (
        echo.
        echo 🎉 正在启动极简数字时钟...
        start "" "%outputDir%\%exeName%"
    )
) else (
    echo ❌ 未找到编译后的文件
)

echo.
echo 💡 提示: 
echo • 推荐使用选项1（单文件EXE）获得最佳兼容性
echo • 如需分发给他人，选项1或3最合适
echo • 选项2适合开发环境或已安装.NET的电脑
echo.
pause
