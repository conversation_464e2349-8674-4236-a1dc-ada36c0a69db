using System;
using System.Collections.Generic;
using System.IO;
using System.Windows;
using System.Windows.Media;
using System.Linq;

namespace WorkingClock;

/// <summary>
/// 字体管理器 - 处理自定义字体加载
/// </summary>
public static class FontManager
{
    private static readonly Dictionary<string, FontFamily> customFonts = new();
    private static bool fontsLoaded = false;

    /// <summary>
    /// 获取所有可用字体（包括自定义字体）
    /// </summary>
    public static List<string> GetAvailableFonts()
    {
        LoadCustomFonts();
        
        var fonts = new List<string>();
        
        // 添加自定义字体
        foreach (var customFont in customFonts.Keys)
        {
            fonts.Add(customFont);
        }
        
        // 添加系统字体
        var systemFonts = new List<string>
        {
            "Segoe UI",
            "Arial",
            "Consolas",
            "Times New Roman",
            "Calibri",
            "Microsoft YaHei",
            "SimSun",
            "Courier New",
            "Verdana",
            "Tahoma"
        };
        
        fonts.AddRange(systemFonts);
        
        return fonts.Distinct().ToList();
    }

    /// <summary>
    /// 获取字体族
    /// </summary>
    public static FontFamily GetFontFamily(string fontName)
    {
        LoadCustomFonts();
        
        // 首先检查自定义字体
        if (customFonts.ContainsKey(fontName))
        {
            return customFonts[fontName];
        }
        
        // 如果不是自定义字体，返回系统字体
        try
        {
            return new FontFamily(fontName);
        }
        catch
        {
            // 如果字体不存在，返回默认字体
            return new FontFamily("Segoe UI");
        }
    }

    /// <summary>
    /// 加载自定义字体
    /// </summary>
    private static void LoadCustomFonts()
    {
        if (fontsLoaded) return;
        
        try
        {
            var fontsPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Fonts");
            
            if (Directory.Exists(fontsPath))
            {
                var fontFiles = Directory.GetFiles(fontsPath, "*.ttf");
                
                foreach (var fontFile in fontFiles)
                {
                    try
                    {
                        var fontName = Path.GetFileNameWithoutExtension(fontFile);
                        var fontUri = new Uri($"file:///{fontFile.Replace('\\', '/')}");
                        var fontFamily = new FontFamily(fontUri, fontName);
                        
                        // 特殊处理DS-Digital字体
                        if (fontName.Contains("DS-Digital") || fontName.Contains("DS_Digital"))
                        {
                            customFonts["DS-Digital"] = fontFamily;
                        }
                        else
                        {
                            customFonts[fontName] = fontFamily;
                        }
                        
                        System.Diagnostics.Debug.WriteLine($"已加载自定义字体: {fontName}");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"加载字体失败 {fontFile}: {ex.Message}");
                    }
                }
            }
            
            // 如果没有找到DS-Digital字体文件，创建一个模拟的LED字体
            if (!customFonts.ContainsKey("DS-Digital"))
            {
                CreateSimulatedDigitalFont();
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"加载自定义字体时出错: {ex.Message}");
        }
        
        fontsLoaded = true;
    }

    /// <summary>
    /// 创建模拟的数字字体（当没有DS-Digital字体文件时）
    /// </summary>
    private static void CreateSimulatedDigitalFont()
    {
        try
        {
            // 使用Consolas作为DS-Digital的替代，因为它是等宽字体，比较接近数字显示效果
            customFonts["DS-Digital"] = new FontFamily("Consolas");
            System.Diagnostics.Debug.WriteLine("使用Consolas作为DS-Digital字体的替代");
        }
        catch
        {
            // 如果Consolas也不可用，使用Courier New
            customFonts["DS-Digital"] = new FontFamily("Courier New");
            System.Diagnostics.Debug.WriteLine("使用Courier New作为DS-Digital字体的替代");
        }
    }

    /// <summary>
    /// 检查是否为数字字体
    /// </summary>
    public static bool IsDigitalFont(string fontName)
    {
        return fontName.Contains("DS-Digital") || 
               fontName.Contains("Digital") || 
               fontName.Contains("LED") || 
               fontName.Contains("LCD");
    }

    /// <summary>
    /// 获取字体显示名称
    /// </summary>
    public static string GetFontDisplayName(string fontName)
    {
        return fontName switch
        {
            "DS-Digital" => "DS-Digital (LED数字)",
            "Segoe UI" => "Segoe UI (默认)",
            "Consolas" => "Consolas (等宽)",
            "Arial" => "Arial (经典)",
            "Times New Roman" => "Times New Roman (衬线)",
            "Calibri" => "Calibri (现代)",
            "Microsoft YaHei" => "Microsoft YaHei (微软雅黑)",
            "SimSun" => "SimSun (宋体)",
            "Courier New" => "Courier New (打字机)",
            "Verdana" => "Verdana (清晰)",
            "Tahoma" => "Tahoma (紧凑)",
            _ => fontName
        };
    }

    /// <summary>
    /// 获取推荐的字体大小
    /// </summary>
    public static double GetRecommendedFontSize(string fontName, double currentSize)
    {
        // DS-Digital字体通常需要稍大一些的尺寸来获得最佳效果
        if (IsDigitalFont(fontName))
        {
            return Math.Max(currentSize * 1.1, currentSize + 4);
        }
        
        return currentSize;
    }

    /// <summary>
    /// 获取字体的建议颜色
    /// </summary>
    public static string GetRecommendedFontColor(string fontName)
    {
        // DS-Digital字体通常用绿色或红色来模拟LED效果
        if (IsDigitalFont(fontName))
        {
            return "LimeGreen"; // LED绿色
        }
        
        return "White"; // 默认白色
    }

    /// <summary>
    /// 检查字体文件是否存在
    /// </summary>
    public static bool IsFontFileAvailable(string fontName)
    {
        if (fontName == "DS-Digital")
        {
            var fontsPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Fonts");
            var dsDigitalPath = Path.Combine(fontsPath, "DS-Digital.ttf");
            return File.Exists(dsDigitalPath);
        }
        
        return false;
    }

    /// <summary>
    /// 获取字体文件下载提示
    /// </summary>
    public static string GetFontDownloadHint(string fontName)
    {
        if (fontName == "DS-Digital" && !IsFontFileAvailable(fontName))
        {
            return "提示：要获得最佳LED效果，请下载DS-Digital.ttf字体文件并放置在Fonts文件夹中。\n" +
                   "当前使用等宽字体作为替代。";
        }
        
        return string.Empty;
    }
}
