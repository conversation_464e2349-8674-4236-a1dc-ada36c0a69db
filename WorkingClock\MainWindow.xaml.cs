﻿using System;
using System.Windows;
using System.Windows.Input;
using System.Windows.Threading;
using System.Windows.Media;
using System.Windows.Media.Effects;

namespace WorkingClock;

/// <summary>
/// 极简数字时钟主窗口
/// </summary>
public partial class MainWindow : Window
{
    private DispatcherTimer clockTimer = null!;
    private bool is24HourFormat = true;
    private SettingsWindow? settingsWindow;

    public MainWindow()
    {
        InitializeComponent();
        InitializeClock();
    }

    /// <summary>
    /// 初始化时钟
    /// </summary>
    private void InitializeClock()
    {
        clockTimer = new DispatcherTimer();
        clockTimer.Interval = TimeSpan.FromSeconds(1);
        clockTimer.Tick += ClockTimer_Tick;
        clockTimer.Start();
        UpdateTimeDisplay();
    }

    /// <summary>
    /// 时钟定时器事件
    /// </summary>
    private void ClockTimer_Tick(object? sender, EventArgs e)
    {
        UpdateTimeDisplay();
    }

    /// <summary>
    /// 更新时间显示
    /// </summary>
    private void UpdateTimeDisplay()
    {
        var now = DateTime.Now;

        // 更新时间
        if (is24HourFormat)
        {
            TimeTextBlock.Text = now.ToString("HH:mm:ss");
            StatusText.Text = "24H";
        }
        else
        {
            TimeTextBlock.Text = now.ToString("hh:mm:ss tt");
            StatusText.Text = "12H";
        }

        // 更新日期
        DateTextBlock.Text = now.ToString("yyyy年MM月dd日 dddd");
    }

    /// <summary>
    /// 窗口拖动事件
    /// </summary>
    private void Border_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
    {
        if (e.ButtonState == MouseButtonState.Pressed)
        {
            this.DragMove();
        }
    }

    /// <summary>
    /// 设置菜单点击事件
    /// </summary>
    private void SettingsMenuItem_Click(object sender, RoutedEventArgs e)
    {
        ShowSettingsWindow();
    }

    /// <summary>
    /// 显示设置窗口
    /// </summary>
    private void ShowSettingsWindow()
    {
        if (settingsWindow == null || !settingsWindow.IsLoaded)
        {
            settingsWindow = new SettingsWindow();
            settingsWindow.Owner = this;
            settingsWindow.SettingsChanged += OnSettingsChanged;
        }
        settingsWindow.Show();
        settingsWindow.Activate();
    }

    /// <summary>
    /// 设置更改事件处理
    /// </summary>
    private void OnSettingsChanged()
    {
        if (settingsWindow != null)
        {
            ApplySettings(settingsWindow);
        }
    }

    /// <summary>
    /// 应用设置到主窗口
    /// </summary>
    private void ApplySettings(SettingsWindow settings)
    {
        // 应用边框设置
        MainBorder.CornerRadius = new CornerRadius(settings.BorderRadius);
        MainBorder.BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString(settings.BorderColor));

        // 应用背景设置
        var bgColor = (Color)ColorConverter.ConvertFromString(settings.BackgroundColor);
        bgColor.A = (byte)(settings.BackgroundOpacity * 255);
        MainBorder.Background = new SolidColorBrush(bgColor);

        // 应用字体设置
        TimeTextBlock.FontFamily = new FontFamily(settings.FontFamily);
        TimeTextBlock.FontSize = settings.FontSize;
        TimeTextBlock.Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString(settings.FontColor));
        TimeTextBlock.FontWeight = settings.FontWeight;

        // 应用日期字体设置
        DateTextBlock.FontFamily = new FontFamily(settings.FontFamily);
        DateTextBlock.Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString(settings.FontColor));

        // 应用阴影设置
        var timeEffect = new DropShadowEffect
        {
            Color = Colors.Black,
            Direction = 270,
            ShadowDepth = 2,
            BlurRadius = settings.ShadowBlur,
            Opacity = settings.ShadowOpacity
        };
        TimeTextBlock.Effect = timeEffect;

        var dateEffect = new DropShadowEffect
        {
            Color = Colors.Black,
            Direction = 270,
            ShadowDepth = 1,
            BlurRadius = settings.ShadowBlur * 0.5,
            Opacity = settings.ShadowOpacity * 0.6
        };
        DateTextBlock.Effect = dateEffect;

        // 应用窗口大小设置
        var sizeParts = settings.WindowSize.Split(',');
        if (sizeParts.Length == 2 &&
            double.TryParse(sizeParts[0], out double width) &&
            double.TryParse(sizeParts[1], out double height))
        {
            this.Width = width;
            this.Height = height;
        }

        // 应用窗口位置设置
        ApplyWindowPosition(settings.WindowPosition);
    }

    /// <summary>
    /// 应用窗口位置
    /// </summary>
    private void ApplyWindowPosition(string position)
    {
        var screenWidth = SystemParameters.PrimaryScreenWidth;
        var screenHeight = SystemParameters.PrimaryScreenHeight;

        switch (position)
        {
            case "Center":
                this.WindowStartupLocation = WindowStartupLocation.CenterScreen;
                this.Left = (screenWidth - this.Width) / 2;
                this.Top = (screenHeight - this.Height) / 2;
                break;
            case "TopLeft":
                this.Left = 50;
                this.Top = 50;
                break;
            case "TopRight":
                this.Left = screenWidth - this.Width - 50;
                this.Top = 50;
                break;
            case "BottomLeft":
                this.Left = 50;
                this.Top = screenHeight - this.Height - 100;
                break;
            case "BottomRight":
                this.Left = screenWidth - this.Width - 50;
                this.Top = screenHeight - this.Height - 100;
                break;
        }
    }

    /// <summary>
    /// 置顶菜单点击事件
    /// </summary>
    private void TopMostMenuItem_Click(object sender, RoutedEventArgs e)
    {
        this.Topmost = !this.Topmost;
        TopMostMenuItem.Header = this.Topmost ? "📌 取消置顶" : "📌 置顶";
    }

    /// <summary>
    /// 12小时制菜单点击事件
    /// </summary>
    private void Hour12MenuItem_Click(object sender, RoutedEventArgs e)
    {
        is24HourFormat = false;
        UpdateTimeDisplay();
    }

    /// <summary>
    /// 24小时制菜单点击事件
    /// </summary>
    private void Hour24MenuItem_Click(object sender, RoutedEventArgs e)
    {
        is24HourFormat = true;
        UpdateTimeDisplay();
    }

    /// <summary>
    /// 关于菜单点击事件
    /// </summary>
    private void AboutMenuItem_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("极简数字时钟 v1.0\nWindows 11 现代化数字时钟应用\n\n功能特色：\n• 实时时间显示\n• 12/24小时制切换\n• 窗口置顶功能\n• 拖动移动窗口\n• 半透明现代化界面",
                       "关于",
                       MessageBoxButton.OK,
                       MessageBoxImage.Information);
    }

    /// <summary>
    /// 退出菜单点击事件
    /// </summary>
    private void ExitMenuItem_Click(object sender, RoutedEventArgs e)
    {
        Application.Current.Shutdown();
    }
}