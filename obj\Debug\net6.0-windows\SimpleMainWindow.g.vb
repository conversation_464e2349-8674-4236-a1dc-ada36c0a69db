﻿#ExternalChecksum("..\..\..\SimpleMainWindow.xaml","{ff1816ec-aa5e-4d10-87f7-6f4963833460}","A2016456FFA8EA1177B3C68D158E89448035835E")
'------------------------------------------------------------------------------
' <auto-generated>
'     此代码由工具生成。
'     运行时版本:4.0.30319.42000
'
'     对此文件的更改可能会导致不正确的行为，并且如果
'     重新生成代码，这些更改将会丢失。
' </auto-generated>
'------------------------------------------------------------------------------

Option Strict Off
Option Explicit On

Imports System
Imports System.Diagnostics
Imports System.Windows
Imports System.Windows.Automation
Imports System.Windows.Controls
Imports System.Windows.Controls.Primitives
Imports System.Windows.Controls.Ribbon
Imports System.Windows.Data
Imports System.Windows.Documents
Imports System.Windows.Forms.Integration
Imports System.Windows.Ink
Imports System.Windows.Input
Imports System.Windows.Markup
Imports System.Windows.Media
Imports System.Windows.Media.Animation
Imports System.Windows.Media.Effects
Imports System.Windows.Media.Imaging
Imports System.Windows.Media.Media3D
Imports System.Windows.Media.TextFormatting
Imports System.Windows.Navigation
Imports System.Windows.Shapes
Imports System.Windows.Shell

Namespace SimpleDigitalClock
    
    '''<summary>
    '''MainWindow
    '''</summary>
    <Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>  _
    Partial Public Class MainWindow
        Inherits System.Windows.Window
        Implements System.Windows.Markup.IComponentConnector
        
        
        #ExternalSource("..\..\..\SimpleMainWindow.xaml",16)
        <System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")>  _
        Friend WithEvents MainBorder As System.Windows.Controls.Border
        
        #End ExternalSource
        
        
        #ExternalSource("..\..\..\SimpleMainWindow.xaml",36)
        <System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")>  _
        Friend WithEvents TimeTextBlock As System.Windows.Controls.TextBlock
        
        #End ExternalSource
        
        
        #ExternalSource("..\..\..\SimpleMainWindow.xaml",51)
        <System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")>  _
        Friend WithEvents DateTextBlock As System.Windows.Controls.TextBlock
        
        #End ExternalSource
        
        
        #ExternalSource("..\..\..\SimpleMainWindow.xaml",73)
        <System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")>  _
        Friend WithEvents StatusText As System.Windows.Controls.TextBlock
        
        #End ExternalSource
        
        
        #ExternalSource("..\..\..\SimpleMainWindow.xaml",85)
        <System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")>  _
        Friend WithEvents TopMostMenuItem As System.Windows.Controls.MenuItem
        
        #End ExternalSource
        
        
        #ExternalSource("..\..\..\SimpleMainWindow.xaml",87)
        <System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")>  _
        Friend WithEvents Hour12MenuItem As System.Windows.Controls.MenuItem
        
        #End ExternalSource
        
        
        #ExternalSource("..\..\..\SimpleMainWindow.xaml",88)
        <System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")>  _
        Friend WithEvents Hour24MenuItem As System.Windows.Controls.MenuItem
        
        #End ExternalSource
        
        Private _contentLoaded As Boolean
        
        '''<summary>
        '''InitializeComponent
        '''</summary>
        <System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")>  _
        Public Sub InitializeComponent() Implements System.Windows.Markup.IComponentConnector.InitializeComponent
            If _contentLoaded Then
                Return
            End If
            _contentLoaded = true
            Dim resourceLocater As System.Uri = New System.Uri("/SimpleDigitalClock;V1.0.0.0;component/simplemainwindow.xaml", System.UriKind.Relative)
            
            #ExternalSource("..\..\..\SimpleMainWindow.xaml",1)
            System.Windows.Application.LoadComponent(Me, resourceLocater)
            
            #End ExternalSource
        End Sub
        
        <System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0"),  _
         System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never),  _
         System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes"),  _
         System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity"),  _
         System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")>  _
        Sub System_Windows_Markup_IComponentConnector_Connect(ByVal connectionId As Integer, ByVal target As Object) Implements System.Windows.Markup.IComponentConnector.Connect
            If (connectionId = 1) Then
                Me.MainBorder = CType(target,System.Windows.Controls.Border)
                
                #ExternalSource("..\..\..\SimpleMainWindow.xaml",21)
                AddHandler Me.MainBorder.MouseLeftButtonDown, New System.Windows.Input.MouseButtonEventHandler(AddressOf Me.Border_MouseLeftButtonDown)
                
                #End ExternalSource
                Return
            End If
            If (connectionId = 2) Then
                Me.TimeTextBlock = CType(target,System.Windows.Controls.TextBlock)
                Return
            End If
            If (connectionId = 3) Then
                Me.DateTextBlock = CType(target,System.Windows.Controls.TextBlock)
                Return
            End If
            If (connectionId = 4) Then
                Me.StatusText = CType(target,System.Windows.Controls.TextBlock)
                Return
            End If
            If (connectionId = 5) Then
                Me.TopMostMenuItem = CType(target,System.Windows.Controls.MenuItem)
                
                #ExternalSource("..\..\..\SimpleMainWindow.xaml",85)
                AddHandler Me.TopMostMenuItem.Click, New System.Windows.RoutedEventHandler(AddressOf Me.TopMostMenuItem_Click)
                
                #End ExternalSource
                Return
            End If
            If (connectionId = 6) Then
                Me.Hour12MenuItem = CType(target,System.Windows.Controls.MenuItem)
                
                #ExternalSource("..\..\..\SimpleMainWindow.xaml",87)
                AddHandler Me.Hour12MenuItem.Click, New System.Windows.RoutedEventHandler(AddressOf Me.Hour12MenuItem_Click)
                
                #End ExternalSource
                Return
            End If
            If (connectionId = 7) Then
                Me.Hour24MenuItem = CType(target,System.Windows.Controls.MenuItem)
                
                #ExternalSource("..\..\..\SimpleMainWindow.xaml",88)
                AddHandler Me.Hour24MenuItem.Click, New System.Windows.RoutedEventHandler(AddressOf Me.Hour24MenuItem_Click)
                
                #End ExternalSource
                Return
            End If
            If (connectionId = 8) Then
                
                #ExternalSource("..\..\..\SimpleMainWindow.xaml",90)
                AddHandler CType(target,System.Windows.Controls.MenuItem).Click, New System.Windows.RoutedEventHandler(AddressOf Me.AboutMenuItem_Click)
                
                #End ExternalSource
                Return
            End If
            If (connectionId = 9) Then
                
                #ExternalSource("..\..\..\SimpleMainWindow.xaml",91)
                AddHandler CType(target,System.Windows.Controls.MenuItem).Click, New System.Windows.RoutedEventHandler(AddressOf Me.ExitMenuItem_Click)
                
                #End ExternalSource
                Return
            End If
            Me._contentLoaded = true
        End Sub
    End Class
End Namespace

