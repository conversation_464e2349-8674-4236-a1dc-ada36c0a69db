Imports System.Windows
Imports System.Windows.Media

''' <summary>
''' 设置窗口类
''' </summary>
Class SettingsWindow
    Inherits Window

    ' 设置更改事件
    Public Event SettingsChanged()

    ' 临时设置变量
    Private tempFontFamily As String
    Private tempFontSize As Double
    Private tempFontColor As Color
    Private tempOpacity As Double
    Private tempIsTopMost As Boolean
    Private tempIs24HourFormat As Boolean
    Private tempAutoStart As Boolean

    ''' <summary>
    ''' 构造函数
    ''' </summary>
    Public Sub New()
        InitializeComponent()
        LoadCurrentSettings()
        InitializeControls()
        UpdatePreview()
    End Sub

    ''' <summary>
    ''' 加载当前设置
    ''' </summary>
    Private Sub LoadCurrentSettings()
        tempFontFamily = SettingsManager.FontFamily
        tempFontSize = SettingsManager.FontSize
        tempFontColor = SettingsManager.FontColor
        tempOpacity = SettingsManager.WindowOpacity
        tempIsTopMost = SettingsManager.IsTopMost
        tempIs24HourFormat = SettingsManager.Is24HourFormat
        tempAutoStart = SettingsManager.AutoStart
    End Sub

    ''' <summary>
    ''' 初始化控件
    ''' </summary>
    Private Sub InitializeControls()
        ' 初始化字体列表
        FontFamilyComboBox.Items.Clear()
        For Each font As String In SettingsManager.AvailableFonts
            FontFamilyComboBox.Items.Add(font)
        Next
        FontFamilyComboBox.SelectedItem = tempFontFamily

        ' 初始化颜色列表
        FontColorComboBox.Items.Clear()
        FontColorComboBox.Items.Add("白色")
        FontColorComboBox.Items.Add("浅蓝色")
        FontColorComboBox.Items.Add("浅绿色")
        FontColorComboBox.Items.Add("黄色")
        FontColorComboBox.Items.Add("橙色")
        FontColorComboBox.Items.Add("粉色")
        FontColorComboBox.Items.Add("浅灰色")
        
        ' 设置当前颜色选择
        Dim colorIndex As Integer = Array.IndexOf(SettingsManager.AvailableColors, tempFontColor)
        If colorIndex >= 0 Then
            FontColorComboBox.SelectedIndex = colorIndex
        End If

        ' 设置字体大小
        FontSizeSlider.Value = tempFontSize
        FontSizeText.Text = tempFontSize.ToString()

        ' 设置透明度
        OpacitySlider.Value = tempOpacity
        OpacityText.Text = $"{CInt(tempOpacity * 100)}%"

        ' 设置复选框
        TopMostCheckBox.IsChecked = tempIsTopMost
        AutoStartCheckBox.IsChecked = tempAutoStart

        ' 设置时间格式
        If tempIs24HourFormat Then
            Hour24RadioButton.IsChecked = True
        Else
            Hour12RadioButton.IsChecked = True
        End If

        ' 更新颜色预览
        UpdateColorPreview()
    End Sub

    ''' <summary>
    ''' 更新颜色预览
    ''' </summary>
    Private Sub UpdateColorPreview()
        ColorPreview.Fill = New SolidColorBrush(tempFontColor)
    End Sub

    ''' <summary>
    ''' 更新预览
    ''' </summary>
    Private Sub UpdatePreview()
        PreviewTextBlock.FontFamily = New FontFamily(tempFontFamily)
        PreviewTextBlock.FontSize = Math.Max(tempFontSize * 0.5, 16) ' 缩放预览字体大小
        PreviewTextBlock.Foreground = New SolidColorBrush(tempFontColor)
        
        ' 更新预览时间格式
        Dim now As DateTime = DateTime.Now
        If tempIs24HourFormat Then
            PreviewTextBlock.Text = now.ToString("HH:mm:ss")
        Else
            PreviewTextBlock.Text = now.ToString("hh:mm:ss tt")
        End If
    End Sub

    ' 事件处理程序
    Private Sub FontFamilyComboBox_SelectionChanged(sender As Object, e As SelectionChangedEventArgs)
        If FontFamilyComboBox.SelectedItem IsNot Nothing Then
            tempFontFamily = FontFamilyComboBox.SelectedItem.ToString()
            UpdatePreview()
        End If
    End Sub

    Private Sub FontSizeSlider_ValueChanged(sender As Object, e As RoutedPropertyChangedEventArgs(Of Double))
        tempFontSize = FontSizeSlider.Value
        FontSizeText.Text = CInt(tempFontSize).ToString()
        UpdatePreview()
    End Sub

    Private Sub FontColorComboBox_SelectionChanged(sender As Object, e As SelectionChangedEventArgs)
        If FontColorComboBox.SelectedIndex >= 0 AndAlso FontColorComboBox.SelectedIndex < SettingsManager.AvailableColors.Length Then
            tempFontColor = SettingsManager.AvailableColors(FontColorComboBox.SelectedIndex)
            UpdateColorPreview()
            UpdatePreview()
        End If
    End Sub

    Private Sub OpacitySlider_ValueChanged(sender As Object, e As RoutedPropertyChangedEventArgs(Of Double))
        tempOpacity = OpacitySlider.Value
        OpacityText.Text = $"{CInt(tempOpacity * 100)}%"
    End Sub

    Private Sub TopMostCheckBox_Checked(sender As Object, e As RoutedEventArgs)
        tempIsTopMost = True
    End Sub

    Private Sub TopMostCheckBox_Unchecked(sender As Object, e As RoutedEventArgs)
        tempIsTopMost = False
    End Sub

    Private Sub AutoStartCheckBox_Checked(sender As Object, e As RoutedEventArgs)
        tempAutoStart = True
    End Sub

    Private Sub AutoStartCheckBox_Unchecked(sender As Object, e As RoutedEventArgs)
        tempAutoStart = False
    End Sub

    Private Sub Hour12RadioButton_Checked(sender As Object, e As RoutedEventArgs)
        tempIs24HourFormat = False
        UpdatePreview()
    End Sub

    Private Sub Hour24RadioButton_Checked(sender As Object, e As RoutedEventArgs)
        tempIs24HourFormat = True
        UpdatePreview()
    End Sub

    Private Sub ResetButton_Click(sender As Object, e As RoutedEventArgs)
        SettingsManager.ResetToDefaults()
        LoadCurrentSettings()
        InitializeControls()
        UpdatePreview()
    End Sub

    Private Sub ApplyButton_Click(sender As Object, e As RoutedEventArgs)
        ApplySettings()
    End Sub

    Private Sub OkButton_Click(sender As Object, e As RoutedEventArgs)
        ApplySettings()
        Me.Close()
    End Sub

    Private Sub CancelButton_Click(sender As Object, e As RoutedEventArgs)
        Me.Close()
    End Sub

    ''' <summary>
    ''' 应用设置
    ''' </summary>
    Private Sub ApplySettings()
        SettingsManager.FontFamily = tempFontFamily
        SettingsManager.FontSize = tempFontSize
        SettingsManager.FontColor = tempFontColor
        SettingsManager.WindowOpacity = tempOpacity
        SettingsManager.IsTopMost = tempIsTopMost
        SettingsManager.Is24HourFormat = tempIs24HourFormat
        SettingsManager.AutoStart = tempAutoStart

        ' 应用开机自启设置
        Try
            SystemIntegrationHelper.SetAutoStart(tempAutoStart)
        Catch ex As Exception
            MessageBox.Show($"设置开机自启失败: {ex.Message}", "警告", MessageBoxButton.OK, MessageBoxImage.Warning)
        End Try

        SettingsManager.SaveSettings()
        RaiseEvent SettingsChanged()
    End Sub
End Class
