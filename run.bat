@echo off
echo ========================================
echo    极简数字时钟 - 运行指南
echo ========================================
echo.

REM 检查是否安装了.NET 6
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到 .NET 6 SDK
    echo 请从 https://dotnet.microsoft.com/download 下载并安装 .NET 6 SDK
    pause
    exit /b 1
)

echo ✓ .NET 6 SDK 已安装
echo.
echo 您的.NET运行时版本：
dotnet --list-runtimes | findstr "Microsoft.WindowsDesktop.App 6"
echo.

echo 可用的运行选项：
echo.
echo [1] 运行简化版时钟 (推荐 - 已测试可用)
echo [2] 尝试运行完整版VB.NET项目
echo [3] 创建新的简单时钟项目
echo [4] 使用Visual Studio打开项目
echo.
set /p choice="请选择 (1-4): "

if "%choice%"=="1" (
    echo.
    echo 正在运行简化版时钟...
    if exist "WorkingClock" (
        cd WorkingClock
        dotnet run
        cd ..
    ) else (
        echo 简化版项目不存在，正在创建...
        call create-simple-clock.bat
    )
) else if "%choice%"=="2" (
    echo.
    echo 尝试运行VB.NET完整版项目...
    echo 注意：由于VB.NET WPF项目的技术限制，可能需要Visual Studio
    dotnet run --project DigitalClock.vbproj
) else if "%choice%"=="3" (
    echo.
    call create-simple-clock.bat
) else if "%choice%"=="4" (
    echo.
    echo 使用Visual Studio 2022打开项目的步骤：
    echo 1. 启动Visual Studio 2022
    echo 2. 选择"打开项目或解决方案"
    echo 3. 选择 DigitalClock.vbproj 文件
    echo 4. 按 F5 运行项目
    echo.
    echo 或者直接运行以下命令：
    echo start DigitalClock.vbproj
    echo.
    set /p openVS="是否现在打开项目文件？(y/n): "
    if /i "%openVS%"=="y" start DigitalClock.vbproj
) else (
    echo 无效选择，退出...
)

echo.
pause
