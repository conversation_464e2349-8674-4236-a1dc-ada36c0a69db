using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Threading;

namespace WorkingClock;

/// <summary>
/// HTC Sense风格的时钟面板
/// </summary>
public class HTCSenseClockPanel : UserControl
{
    private Grid mainGrid;
    private HTCSenseFlipClock hourTens;
    private HTCSenseFlipClock hourOnes;
    private HTCSenseFlipClock minuteTens;
    private HTCSenseFlipClock minuteOnes;
    private HTCSenseFlipClock secondTens;
    private HTCSenseFlipClock secondOnes;
    
    private TextBlock colonHM;
    private TextBlock colonMS;
    private TextBlock dateLabel;
    private TextBlock weatherLabel;
    
    private DispatcherTimer updateTimer;
    private bool is24HourFormat = true;
    private bool showSeconds = true;

    public HTCSenseClockPanel()
    {
        InitializeComponent();
        StartTimer();
    }

    /// <summary>
    /// 初始化组件
    /// </summary>
    private void InitializeComponent()
    {
        Width = 600;
        Height = 200;
        
        // 主网格
        mainGrid = new Grid();
        Content = mainGrid;

        // 创建行定义
        mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Auto) });
        mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });
        mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Auto) });

        // 创建时钟面板
        CreateClockPanel();
        
        // 创建日期和天气标签
        CreateLabels();
        
        // 应用默认样式
        ApplyDefaultStyle();
    }

    /// <summary>
    /// 创建时钟面板
    /// </summary>
    private void CreateClockPanel()
    {
        var clockPanel = new StackPanel
        {
            Orientation = Orientation.Horizontal,
            HorizontalAlignment = HorizontalAlignment.Center,
            VerticalAlignment = VerticalAlignment.Center,
            Margin = new Thickness(20)
        };

        Grid.SetRow(clockPanel, 1);

        // 小时
        hourTens = new HTCSenseFlipClock();
        hourOnes = new HTCSenseFlipClock();
        
        // 分钟
        minuteTens = new HTCSenseFlipClock();
        minuteOnes = new HTCSenseFlipClock();
        
        // 秒钟
        secondTens = new HTCSenseFlipClock();
        secondOnes = new HTCSenseFlipClock();

        // 冒号分隔符
        colonHM = CreateColonSeparator();
        colonMS = CreateColonSeparator();

        // 添加到面板
        clockPanel.Children.Add(hourTens);
        clockPanel.Children.Add(hourOnes);
        clockPanel.Children.Add(colonHM);
        clockPanel.Children.Add(minuteTens);
        clockPanel.Children.Add(minuteOnes);
        
        if (showSeconds)
        {
            clockPanel.Children.Add(colonMS);
            clockPanel.Children.Add(secondTens);
            clockPanel.Children.Add(secondOnes);
        }

        mainGrid.Children.Add(clockPanel);
    }

    /// <summary>
    /// 创建冒号分隔符
    /// </summary>
    private TextBlock CreateColonSeparator()
    {
        return new TextBlock
        {
            Text = ":",
            FontSize = 48,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush(Colors.White),
            VerticalAlignment = VerticalAlignment.Center,
            Margin = new Thickness(10, 0, 10, 0),
            Effect = new System.Windows.Media.Effects.DropShadowEffect
            {
                Color = Colors.Black,
                BlurRadius = 3,
                ShadowDepth = 2,
                Opacity = 0.5
            }
        };
    }

    /// <summary>
    /// 创建标签
    /// </summary>
    private void CreateLabels()
    {
        // 日期标签
        dateLabel = new TextBlock
        {
            Text = DateTime.Now.ToString("yyyy年MM月dd日 dddd"),
            FontSize = 16,
            FontWeight = FontWeights.Normal,
            Foreground = new SolidColorBrush(Colors.White),
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 10, 0, 0),
            Opacity = 0.9,
            Effect = new System.Windows.Media.Effects.DropShadowEffect
            {
                Color = Colors.Black,
                BlurRadius = 2,
                ShadowDepth = 1,
                Opacity = 0.3
            }
        };

        Grid.SetRow(dateLabel, 0);
        mainGrid.Children.Add(dateLabel);

        // 天气标签
        weatherLabel = new TextBlock
        {
            Text = "🌤️ 22°C 晴朗",
            FontSize = 14,
            FontWeight = FontWeights.Normal,
            Foreground = new SolidColorBrush(Colors.White),
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 0, 0, 10),
            Opacity = 0.8,
            Effect = new System.Windows.Media.Effects.DropShadowEffect
            {
                Color = Colors.Black,
                BlurRadius = 2,
                ShadowDepth = 1,
                Opacity = 0.3
            }
        };

        Grid.SetRow(weatherLabel, 2);
        mainGrid.Children.Add(weatherLabel);
    }

    /// <summary>
    /// 应用默认样式
    /// </summary>
    private void ApplyDefaultStyle()
    {
        // 设置背景
        Background = new LinearGradientBrush
        {
            StartPoint = new Point(0, 0),
            EndPoint = new Point(1, 1),
            GradientStops = new GradientStopCollection
            {
                new GradientStop(Color.FromArgb(200, 20, 20, 20), 0.0),
                new GradientStop(Color.FromArgb(180, 40, 40, 40), 0.5),
                new GradientStop(Color.FromArgb(200, 20, 20, 20), 1.0)
            }
        };

        // 设置边框
        var border = new Border
        {
            BorderBrush = new SolidColorBrush(Color.FromArgb(100, 255, 255, 255)),
            BorderThickness = new Thickness(1),
            CornerRadius = new CornerRadius(15)
        };

        // 添加整体阴影
        Effect = new System.Windows.Media.Effects.DropShadowEffect
        {
            Color = Colors.Black,
            BlurRadius = 20,
            ShadowDepth = 10,
            Opacity = 0.4
        };
    }

    /// <summary>
    /// 启动定时器
    /// </summary>
    private void StartTimer()
    {
        updateTimer = new DispatcherTimer
        {
            Interval = TimeSpan.FromSeconds(1)
        };
        updateTimer.Tick += UpdateTimer_Tick;
        updateTimer.Start();
        
        // 立即更新一次
        UpdateTime();
    }

    /// <summary>
    /// 定时器事件
    /// </summary>
    private void UpdateTimer_Tick(object? sender, EventArgs e)
    {
        UpdateTime();
    }

    /// <summary>
    /// 更新时间显示
    /// </summary>
    private void UpdateTime()
    {
        var now = DateTime.Now;
        
        // 获取时间组件
        int hour = is24HourFormat ? now.Hour : (now.Hour % 12 == 0 ? 12 : now.Hour % 12);
        int minute = now.Minute;
        int second = now.Second;

        // 更新翻页时钟
        hourTens.SetValue((hour / 10).ToString());
        hourOnes.SetValue((hour % 10).ToString());
        minuteTens.SetValue((minute / 10).ToString());
        minuteOnes.SetValue((minute % 10).ToString());
        
        if (showSeconds)
        {
            secondTens.SetValue((second / 10).ToString());
            secondOnes.SetValue((second % 10).ToString());
        }

        // 更新日期
        dateLabel.Text = now.ToString("yyyy年MM月dd日 dddd");
    }

    /// <summary>
    /// 设置12/24小时制
    /// </summary>
    public void SetTimeFormat(bool is24Hour)
    {
        is24HourFormat = is24Hour;
        UpdateTime();
    }

    /// <summary>
    /// 设置是否显示秒钟
    /// </summary>
    public void SetShowSeconds(bool show)
    {
        showSeconds = show;
        
        if (colonMS != null)
            colonMS.Visibility = show ? Visibility.Visible : Visibility.Collapsed;
        if (secondTens != null)
            secondTens.Visibility = show ? Visibility.Visible : Visibility.Collapsed;
        if (secondOnes != null)
            secondOnes.Visibility = show ? Visibility.Visible : Visibility.Collapsed;
    }

    /// <summary>
    /// 设置字体样式
    /// </summary>
    public void SetFontStyle(FontFamily fontFamily, double fontSize, Brush foreground)
    {
        hourTens?.SetFontStyle(fontFamily, fontSize, foreground);
        hourOnes?.SetFontStyle(fontFamily, fontSize, foreground);
        minuteTens?.SetFontStyle(fontFamily, fontSize, foreground);
        minuteOnes?.SetFontStyle(fontFamily, fontSize, foreground);
        secondTens?.SetFontStyle(fontFamily, fontSize, foreground);
        secondOnes?.SetFontStyle(fontFamily, fontSize, foreground);

        if (colonHM != null)
        {
            colonHM.FontFamily = fontFamily;
            colonHM.FontSize = fontSize * 1.2;
            colonHM.Foreground = foreground;
        }

        if (colonMS != null)
        {
            colonMS.FontFamily = fontFamily;
            colonMS.FontSize = fontSize * 1.2;
            colonMS.Foreground = foreground;
        }
    }

    /// <summary>
    /// 设置颜色主题
    /// </summary>
    public void SetColorTheme(Color primaryColor, Color secondaryColor)
    {
        hourTens?.SetColorTheme(primaryColor, secondaryColor);
        hourOnes?.SetColorTheme(primaryColor, secondaryColor);
        minuteTens?.SetColorTheme(primaryColor, secondaryColor);
        minuteOnes?.SetColorTheme(primaryColor, secondaryColor);
        secondTens?.SetColorTheme(primaryColor, secondaryColor);
        secondOnes?.SetColorTheme(primaryColor, secondaryColor);
    }

    /// <summary>
    /// 更新天气信息
    /// </summary>
    public void UpdateWeather(string weatherText)
    {
        if (weatherLabel != null)
        {
            weatherLabel.Text = weatherText;
        }
    }

    /// <summary>
    /// 停止定时器
    /// </summary>
    public void StopTimer()
    {
        updateTimer?.Stop();
    }

    /// <summary>
    /// 析构函数
    /// </summary>
    ~HTCSenseClockPanel()
    {
        StopTimer();
    }
}
