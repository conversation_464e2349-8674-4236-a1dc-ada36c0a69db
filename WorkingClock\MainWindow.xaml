<Window x:Class="WorkingClock.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:WorkingClock"
        mc:Ignorable="d"
        Title="极简数字时钟"
        Height="200"
        Width="400"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        Topmost="False"
        ResizeMode="NoResize"
        ShowInTaskbar="True"
        WindowStartupLocation="CenterScreen">

    <!-- 主窗口背景 -->
    <Border x:Name="MainBorder"
            CornerRadius="10"
            BorderThickness="1"
            BorderBrush="#40FFFFFF"
            MouseLeftButtonDown="Border_MouseLeftButtonDown">

        <!-- 现代化阴影效果 -->
        <Border.Effect>
            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="8" BlurRadius="20" Opacity="0.6"/>
        </Border.Effect>

        <!-- 背景渐变效果 -->
        <Border.Background>
            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#CC000000" Offset="0"/>
                <GradientStop Color="#BB111111" Offset="0.5"/>
                <GradientStop Color="#CC000000" Offset="1"/>
            </LinearGradientBrush>
        </Border.Background>

        <!-- 主要内容区域 -->
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 时间显示区域 -->
            <StackPanel Grid.Row="0"
                       VerticalAlignment="Center"
                       HorizontalAlignment="Center">

                <!-- 时间文本 -->
                <TextBlock x:Name="TimeTextBlock"
                          Text="00:00:00"
                          FontFamily="Segoe UI"
                          FontSize="48"
                          FontWeight="Light"
                          HorizontalAlignment="Center"
                          VerticalAlignment="Center"
                          Margin="0,10"
                          RenderTransformOrigin="0.5,0.5">

                    <!-- 现代化渐变文字效果 -->
                    <TextBlock.Foreground>
                        <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                            <GradientStop Color="#FFFFFF" Offset="0"/>
                            <GradientStop Color="#E0E0E0" Offset="0.5"/>
                            <GradientStop Color="#CCCCCC" Offset="1"/>
                        </LinearGradientBrush>
                    </TextBlock.Foreground>

                    <!-- 增强阴影效果 -->
                    <TextBlock.Effect>
                        <DropShadowEffect Color="Black" Direction="270" ShadowDepth="3" BlurRadius="8" Opacity="0.7"/>
                    </TextBlock.Effect>

                    <!-- 变换组 -->
                    <TextBlock.RenderTransform>
                        <TransformGroup>
                            <ScaleTransform/>
                            <SkewTransform/>
                            <RotateTransform/>
                            <TranslateTransform/>
                        </TransformGroup>
                    </TextBlock.RenderTransform>
                </TextBlock>

                <!-- 日期文本 -->
                <TextBlock x:Name="DateTextBlock"
                          Text="2024年1月1日 星期一"
                          FontFamily="Segoe UI"
                          FontSize="14"
                          FontWeight="Normal"
                          Foreground="White"
                          HorizontalAlignment="Center"
                          VerticalAlignment="Center"
                          Opacity="0.8"
                          Margin="0,0,0,5">
                    <TextBlock.Effect>
                        <DropShadowEffect Color="Black" Direction="270" ShadowDepth="1" BlurRadius="2" Opacity="0.3"/>
                    </TextBlock.Effect>
                </TextBlock>

                <!-- 天气信息区域 -->
                <StackPanel x:Name="WeatherPanel"
                           Orientation="Horizontal"
                           HorizontalAlignment="Center"
                           Margin="0,5,0,10"
                           Visibility="Collapsed">

                    <!-- 天气图标 -->
                    <TextBlock x:Name="WeatherIconTextBlock"
                              Text="🌡️"
                              FontSize="16"
                              Foreground="White"
                              VerticalAlignment="Center"
                              Margin="0,0,5,0">
                        <TextBlock.Effect>
                            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="1" BlurRadius="2" Opacity="0.3"/>
                        </TextBlock.Effect>
                    </TextBlock>

                    <!-- 温度 -->
                    <TextBlock x:Name="TemperatureTextBlock"
                              Text="--°C"
                              FontFamily="Segoe UI"
                              FontSize="14"
                              FontWeight="Normal"
                              Foreground="White"
                              VerticalAlignment="Center"
                              Margin="0,0,8,0">
                        <TextBlock.Effect>
                            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="1" BlurRadius="2" Opacity="0.3"/>
                        </TextBlock.Effect>
                    </TextBlock>

                    <!-- 天气描述 -->
                    <TextBlock x:Name="WeatherDescriptionTextBlock"
                              Text="获取中..."
                              FontFamily="Segoe UI"
                              FontSize="12"
                              FontWeight="Normal"
                              Foreground="White"
                              VerticalAlignment="Center"
                              Opacity="0.9">
                        <TextBlock.Effect>
                            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="1" BlurRadius="2" Opacity="0.3"/>
                        </TextBlock.Effect>
                    </TextBlock>
                </StackPanel>

                <!-- 位置信息 -->
                <TextBlock x:Name="LocationTextBlock"
                          Text=""
                          FontFamily="Segoe UI"
                          FontSize="10"
                          FontWeight="Normal"
                          Foreground="White"
                          HorizontalAlignment="Center"
                          VerticalAlignment="Center"
                          Opacity="0.6"
                          Margin="0,0,0,10"
                          Visibility="Collapsed">
                    <TextBlock.Effect>
                        <DropShadowEffect Color="Black" Direction="270" ShadowDepth="1" BlurRadius="2" Opacity="0.3"/>
                    </TextBlock.Effect>
                </TextBlock>
            </StackPanel>

            <!-- 状态指示器 -->
            <StackPanel Grid.Row="1"
                       Orientation="Horizontal"
                       HorizontalAlignment="Right"
                       Margin="0,0,10,5">

                <TextBlock x:Name="StatusText"
                          Text="24H"
                          FontSize="10"
                          Foreground="White"
                          Opacity="0.6"/>
            </StackPanel>
        </Grid>
    </Border>

    <!-- 现代化右键菜单 -->
    <Window.ContextMenu>
        <ContextMenu x:Name="MainContextMenu"
                     Background="#2D2D30"
                     BorderBrush="#3F3F46"
                     BorderThickness="1"
                     Padding="4">
            <ContextMenu.Resources>
                <Style TargetType="MenuItem">
                    <Setter Property="Foreground" Value="White"/>
                    <Setter Property="Background" Value="Transparent"/>
                    <Setter Property="Padding" Value="8,6"/>
                    <Setter Property="Margin" Value="2"/>
                    <Style.Triggers>
                        <Trigger Property="IsHighlighted" Value="True">
                            <Setter Property="Background" Value="#404040"/>
                        </Trigger>
                    </Style.Triggers>
                </Style>
                <Style TargetType="Separator">
                    <Setter Property="Background" Value="#3F3F46"/>
                    <Setter Property="Margin" Value="4,2"/>
                </Style>
            </ContextMenu.Resources>

            <MenuItem x:Name="SettingsMenuItem" Header="⚙️ 个性化设置" Click="SettingsMenuItem_Click"/>
            <MenuItem x:Name="AnimationMenuItem" Header="🎬 动画效果" Click="AnimationMenuItem_Click"/>
            <MenuItem x:Name="HTCSenseModeMenuItem" Header="📱 HTC Sense模式" Click="HTCSenseModeMenuItem_Click"/>
            <Separator/>
            <MenuItem x:Name="ShowWeatherMenuItem" Header="🌤️ 显示天气" Click="ShowWeatherMenuItem_Click"/>
            <MenuItem x:Name="RefreshWeatherMenuItem" Header="🔄 刷新天气" Click="RefreshWeatherMenuItem_Click"/>
            <Separator/>
            <MenuItem x:Name="TopMostMenuItem" Header="📌 置顶" Click="TopMostMenuItem_Click"/>
            <Separator/>
            <MenuItem x:Name="Hour12MenuItem" Header="🕐 12小时制" Click="Hour12MenuItem_Click"/>
            <MenuItem x:Name="Hour24MenuItem" Header="🕒 24小时制" Click="Hour24MenuItem_Click"/>
            <Separator/>
            <MenuItem x:Name="AboutMenuItem" Header="ℹ️ 关于" Click="AboutMenuItem_Click"/>
            <MenuItem x:Name="ExitMenuItem" Header="❌ 退出" Click="ExitMenuItem_Click"/>
        </ContextMenu>
    </Window.ContextMenu>
</Window>
