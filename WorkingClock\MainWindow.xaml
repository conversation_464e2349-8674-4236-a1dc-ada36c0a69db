﻿<Window x:Class="WorkingClock.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:WorkingClock"
        mc:Ignorable="d"
        Title="极简数字时钟"
        Height="200"
        Width="400"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        Topmost="False"
        ResizeMode="NoResize"
        ShowInTaskbar="True"
        WindowStartupLocation="CenterScreen">

    <!-- 主窗口背景 -->
    <Border x:Name="MainBorder"
            CornerRadius="10"
            Background="#80000000"
            BorderThickness="1"
            BorderBrush="#40FFFFFF"
            MouseLeftButtonDown="Border_MouseLeftButtonDown">

        <!-- 主要内容区域 -->
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 时间显示区域 -->
            <StackPanel Grid.Row="0"
                       VerticalAlignment="Center"
                       HorizontalAlignment="Center">

                <!-- 时间文本 -->
                <TextBlock x:Name="TimeTextBlock"
                          Text="00:00:00"
                          FontFamily="Segoe UI"
                          FontSize="48"
                          FontWeight="Light"
                          Foreground="White"
                          HorizontalAlignment="Center"
                          VerticalAlignment="Center"
                          Margin="0,10">
                    <TextBlock.Effect>
                        <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2" BlurRadius="4" Opacity="0.5"/>
                    </TextBlock.Effect>
                </TextBlock>

                <!-- 日期文本 -->
                <TextBlock x:Name="DateTextBlock"
                          Text="2024年1月1日 星期一"
                          FontFamily="Segoe UI"
                          FontSize="14"
                          FontWeight="Normal"
                          Foreground="White"
                          HorizontalAlignment="Center"
                          VerticalAlignment="Center"
                          Opacity="0.8"
                          Margin="0,0,0,10">
                    <TextBlock.Effect>
                        <DropShadowEffect Color="Black" Direction="270" ShadowDepth="1" BlurRadius="2" Opacity="0.3"/>
                    </TextBlock.Effect>
                </TextBlock>
            </StackPanel>

            <!-- 状态指示器 -->
            <StackPanel Grid.Row="1"
                       Orientation="Horizontal"
                       HorizontalAlignment="Right"
                       Margin="0,0,10,5">

                <TextBlock x:Name="StatusText"
                          Text="24H"
                          FontSize="10"
                          Foreground="White"
                          Opacity="0.6"/>
            </StackPanel>
        </Grid>
    </Border>

    <!-- 右键菜单 -->
    <Window.ContextMenu>
        <ContextMenu>
            <MenuItem Header="⚙️ 个性化设置" Click="SettingsMenuItem_Click"/>
            <Separator/>
            <MenuItem Header="📌 置顶" x:Name="TopMostMenuItem" Click="TopMostMenuItem_Click"/>
            <Separator/>
            <MenuItem Header="🕐 12小时制" x:Name="Hour12MenuItem" Click="Hour12MenuItem_Click"/>
            <MenuItem Header="🕒 24小时制" x:Name="Hour24MenuItem" Click="Hour24MenuItem_Click"/>
            <Separator/>
            <MenuItem Header="ℹ️ 关于" Click="AboutMenuItem_Click"/>
            <MenuItem Header="❌ 退出" Click="ExitMenuItem_Click"/>
        </ContextMenu>
    </Window.ContextMenu>
</Window>
