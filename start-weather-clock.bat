@echo off
echo ========================================
echo    启动极简数字时钟 v2.0
echo    🌤️ 现已支持天气预报功能！
echo ========================================
echo.

if exist "WorkingClock" (
    echo ✨ 新功能特色：
    echo • 🕐 实时时间显示
    echo • 🌤️ 免费天气预报（自动定位）
    echo • ⚙️ 完全个性化设置
    echo • 📌 窗口置顶功能
    echo • 🖱️ 拖动移动窗口
    echo.
    echo 💡 使用提示：
    echo • 右键点击时钟可访问所有功能
    echo • 天气信息每30分钟自动更新
    echo • 支持显示/隐藏天气信息
    echo • 可手动刷新天气数据
    echo.
    echo 🌐 天气数据来源：
    echo • Open-Meteo API（开源免费，无需API Key）
    echo • IP-API 地理定位服务（自动获取位置）
    echo.
    echo 正在启动时钟应用...
    cd WorkingClock
    dotnet run
    cd ..
) else (
    echo 时钟项目不存在，请先运行 create-simple-clock.bat 创建项目
    pause
    exit /b 1
)

echo.
echo 时钟应用已关闭。
pause
