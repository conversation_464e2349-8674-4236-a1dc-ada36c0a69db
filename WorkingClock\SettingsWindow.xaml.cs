using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Effects;

namespace WorkingClock;

/// <summary>
/// 设置窗口
/// </summary>
public partial class SettingsWindow : Window
{
    // 设置更改事件
    public event Action? SettingsChanged;

    // 临时设置变量
    private double tempBorderRadius = 10;
    private string tempBorderColor = "#40FFFFFF";
    private double tempBackgroundOpacity = 0.5;
    private string tempBackgroundColor = "Black";
    private string tempFontFamily = "Segoe UI";
    private double tempFontSize = 48;
    private string tempFontColor = "White";
    private FontWeight tempFontWeight = FontWeights.Light;
    private double tempShadowOpacity = 0.5;
    private double tempShadowBlur = 4;
    private string tempWindowSize = "400,200";
    private string tempWindowPosition = "Center";

    public SettingsWindow()
    {
        InitializeComponent();
        LoadCurrentSettings();
        InitializeControls();
        UpdatePreview();
    }

    /// <summary>
    /// 加载当前设置
    /// </summary>
    private void LoadCurrentSettings()
    {
        // 这里可以从配置文件或主窗口加载当前设置
        // 使用DS-Digital字体的默认值
        tempBorderRadius = 10;
        tempBorderColor = "White";
        tempBackgroundOpacity = 0.8;
        tempBackgroundColor = "Black";
        tempFontFamily = "DS-Digital"; // 默认使用DS-Digital字体
        tempFontSize = 48;
        tempFontColor = "LimeGreen"; // LED绿色效果
        tempFontWeight = FontWeights.Normal; // DS-Digital通常用Normal
        tempShadowOpacity = 0.7; // 增强阴影效果
        tempShadowBlur = 6;
        tempWindowSize = "中等 (400x200)";
        tempWindowPosition = "屏幕中央";
    }

    /// <summary>
    /// 初始化控件
    /// </summary>
    private void InitializeControls()
    {
        // 设置默认选择
        if (BorderStyleComboBox != null) BorderStyleComboBox.SelectedIndex = 0; // 圆角边框
        if (BorderColorComboBox != null) BorderColorComboBox.SelectedIndex = 0; // 白色边框
        if (BackgroundColorComboBox != null) BackgroundColorComboBox.SelectedIndex = 0; // 黑色背景
        if (FontFamilyComboBox != null) FontFamilyComboBox.SelectedIndex = 0; // Segoe UI
        if (FontColorComboBox != null) FontColorComboBox.SelectedIndex = 0; // 白色字体
        if (FontWeightComboBox != null) FontWeightComboBox.SelectedIndex = 0; // 细体
        if (WindowSizeComboBox != null) WindowSizeComboBox.SelectedIndex = 1; // 中等大小
        if (WindowPositionComboBox != null) WindowPositionComboBox.SelectedIndex = 0; // 屏幕中央

        // 设置滑块值
        if (BackgroundOpacitySlider != null) BackgroundOpacitySlider.Value = tempBackgroundOpacity;
        if (FontSizeSlider != null) FontSizeSlider.Value = tempFontSize;
        if (ShadowOpacitySlider != null) ShadowOpacitySlider.Value = tempShadowOpacity;
        if (ShadowBlurSlider != null) ShadowBlurSlider.Value = tempShadowBlur;

        // 更新文本显示
        UpdateSliderTexts();
    }

    /// <summary>
    /// 更新滑块文本显示
    /// </summary>
    private void UpdateSliderTexts()
    {
        if (BackgroundOpacityText != null && BackgroundOpacitySlider != null)
            BackgroundOpacityText.Text = $"{(int)(BackgroundOpacitySlider.Value * 100)}%";

        if (FontSizeText != null && FontSizeSlider != null)
            FontSizeText.Text = $"{(int)FontSizeSlider.Value}px";

        if (ShadowOpacityText != null && ShadowOpacitySlider != null)
            ShadowOpacityText.Text = $"{(int)(ShadowOpacitySlider.Value * 100)}%";

        if (ShadowBlurText != null && ShadowBlurSlider != null)
            ShadowBlurText.Text = $"{(int)ShadowBlurSlider.Value}px";
    }

    /// <summary>
    /// 更新预览
    /// </summary>
    private void UpdatePreview()
    {
        if (PreviewBorder == null || PreviewTextBlock == null) return;

        try
        {
            // 更新预览边框
            PreviewBorder.CornerRadius = new CornerRadius(tempBorderRadius);
            PreviewBorder.BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString(tempBorderColor));

            // 更新预览背景
            var bgColor = (Color)ColorConverter.ConvertFromString(tempBackgroundColor);
            bgColor.A = (byte)(tempBackgroundOpacity * 255);
            PreviewBorder.Background = new SolidColorBrush(bgColor);

            // 更新预览字体 - 使用FontManager
            PreviewTextBlock.FontFamily = FontManager.GetFontFamily(tempFontFamily);
            PreviewTextBlock.FontSize = Math.Max(tempFontSize * 0.5, 16); // 缩放预览字体大小
            PreviewTextBlock.Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString(tempFontColor));
            PreviewTextBlock.FontWeight = tempFontWeight;

            // 更新预览阴影
            if (PreviewTextBlock.Effect is DropShadowEffect shadow)
            {
                shadow.Opacity = tempShadowOpacity;
                shadow.BlurRadius = tempShadowBlur;
            }

            // 更新预览时间 - DS-Digital字体显示特殊效果
            if (FontManager.IsDigitalFont(tempFontFamily))
            {
                PreviewTextBlock.Text = "88:88:88"; // 显示所有段，展示LED效果
            }
            else
            {
                PreviewTextBlock.Text = DateTime.Now.ToString("HH:mm:ss");
            }
        }
        catch (Exception)
        {
            // 如果更新预览失败，静默处理
        }
    }

    // 事件处理程序
    private void BorderStyleComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        if (BorderStyleComboBox.SelectedItem is ComboBoxItem item && item.Tag is string tag)
        {
            tempBorderRadius = double.Parse(tag);
            UpdatePreview();
        }
    }

    private void BorderColorComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        if (BorderColorComboBox.SelectedItem is ComboBoxItem item && item.Tag is string tag)
        {
            tempBorderColor = tag;
            UpdatePreview();
        }
    }

    private void BackgroundOpacitySlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
    {
        tempBackgroundOpacity = BackgroundOpacitySlider.Value;
        UpdateSliderTexts();
        UpdatePreview();
    }

    private void BackgroundColorComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        if (BackgroundColorComboBox.SelectedItem is ComboBoxItem item && item.Tag is string tag)
        {
            tempBackgroundColor = tag;
            UpdatePreview();
        }
    }

    private void FontFamilyComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        if (FontFamilyComboBox.SelectedItem is ComboBoxItem item && item.Tag is string tag)
        {
            tempFontFamily = tag;
            UpdatePreview();
        }
    }

    private void FontSizeSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
    {
        tempFontSize = FontSizeSlider.Value;
        UpdateSliderTexts();
        UpdatePreview();
    }

    private void FontColorComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        if (FontColorComboBox.SelectedItem is ComboBoxItem item && item.Tag is string tag)
        {
            tempFontColor = tag;
            UpdatePreview();
        }
    }

    private void FontWeightComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        if (FontWeightComboBox.SelectedItem is ComboBoxItem item && item.Tag is string tag)
        {
            tempFontWeight = tag switch
            {
                "Light" => FontWeights.Light,
                "Normal" => FontWeights.Normal,
                "Bold" => FontWeights.Bold,
                "Black" => FontWeights.Black,
                _ => FontWeights.Light
            };
            UpdatePreview();
        }
    }

    private void ShadowOpacitySlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
    {
        tempShadowOpacity = ShadowOpacitySlider.Value;
        UpdateSliderTexts();
        UpdatePreview();
    }

    private void ShadowBlurSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
    {
        tempShadowBlur = ShadowBlurSlider.Value;
        UpdateSliderTexts();
        UpdatePreview();
    }

    private void WindowSizeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        if (WindowSizeComboBox.SelectedItem is ComboBoxItem item && item.Tag is string tag)
        {
            tempWindowSize = tag;
        }
    }

    private void WindowPositionComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        if (WindowPositionComboBox.SelectedItem is ComboBoxItem item && item.Tag is string tag)
        {
            tempWindowPosition = tag;
        }
    }

    private void ResetButton_Click(object sender, RoutedEventArgs e)
    {
        // 重置为默认值
        tempBorderRadius = 10;
        tempBorderColor = "#40FFFFFF";
        tempBackgroundOpacity = 0.5;
        tempBackgroundColor = "Black";
        tempFontFamily = "Segoe UI";
        tempFontSize = 48;
        tempFontColor = "White";
        tempFontWeight = FontWeights.Light;
        tempShadowOpacity = 0.5;
        tempShadowBlur = 4;
        tempWindowSize = "400,200";
        tempWindowPosition = "Center";

        InitializeControls();
        UpdatePreview();
    }

    private void ApplyButton_Click(object sender, RoutedEventArgs e)
    {
        ApplySettings();
    }

    private void OkButton_Click(object sender, RoutedEventArgs e)
    {
        ApplySettings();
        this.Close();
    }

    private void CancelButton_Click(object sender, RoutedEventArgs e)
    {
        this.Close();
    }

    /// <summary>
    /// 应用设置
    /// </summary>
    private void ApplySettings()
    {
        // 触发设置更改事件，传递设置给主窗口
        SettingsChanged?.Invoke();
    }

    // 公共属性，供主窗口访问设置值
    public double BorderRadius => tempBorderRadius;
    public string BorderColor => tempBorderColor;
    public double BackgroundOpacity => tempBackgroundOpacity;
    public string BackgroundColor => tempBackgroundColor;
    public new string FontFamily => tempFontFamily;
    public new double FontSize => tempFontSize;
    public string FontColor => tempFontColor;
    public new FontWeight FontWeight => tempFontWeight;
    public double ShadowOpacity => tempShadowOpacity;
    public double ShadowBlur => tempShadowBlur;
    public string WindowSize => tempWindowSize;
    public string WindowPosition => tempWindowPosition;
}
