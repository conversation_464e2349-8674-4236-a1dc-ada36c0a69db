<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <UseWindowsForms>true</UseWindowsForms>
    <ApplicationIcon>clock.ico</ApplicationIcon>
    <AssemblyTitle>极简数字时钟</AssemblyTitle>
    <AssemblyDescription>Windows 11极简数字时钟应用</AssemblyDescription>
    <AssemblyCompany>Digital Clock Studio</AssemblyCompany>
    <AssemblyProduct>Digital Clock</AssemblyProduct>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <StartupObject>DigitalClock.App</StartupObject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.WindowsAPICodePack-Shell" Version="1.1.0" />
    <PackageReference Include="Microsoft.WindowsAPICodePack-Core" Version="1.1.0" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Drawing" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Resources\**" />
  </ItemGroup>

</Project>
