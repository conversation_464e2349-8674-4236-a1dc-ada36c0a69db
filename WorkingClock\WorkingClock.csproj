﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>

    <!-- 优化设置 (WPF兼容) -->
    <PublishTrimmed>false</PublishTrimmed>
    <PublishReadyToRun>false</PublishReadyToRun>
    <PublishSingleFile>true</PublishSingleFile>
    <IncludeNativeLibrariesForSelfExtract>true</IncludeNativeLibrariesForSelfExtract>
    <DebugType>none</DebugType>
    <DebugSymbols>false</DebugSymbols>

    <!-- 压缩和大小优化 -->
    <EnableCompressionInSingleFile>true</EnableCompressionInSingleFile>
    <EventSourceSupport>false</EventSourceSupport>
    <UseSystemResourceKeys>true</UseSystemResourceKeys>
    <OptimizationPreference>Size</OptimizationPreference>

    <!-- 移除不必要的功能 -->
    <UseWindowsForms>false</UseWindowsForms>
    <HttpActivityPropagationSupport>false</HttpActivityPropagationSupport>
    <MetadataUpdaterSupport>false</MetadataUpdaterSupport>
    <InvariantGlobalization>true</InvariantGlobalization>
    <StackTraceSupport>false</StackTraceSupport>
    <UseNETCoreAppRuntimePack>true</UseNETCoreAppRuntimePack>
    <SuppressNETCoreSdkPreviewMessage>true</SuppressNETCoreSdkPreviewMessage>
  </PropertyGroup>

  <!-- 修剪根程序集 -->
  <ItemGroup>
    <TrimmerRootAssembly Include="WorkingClock" />
  </ItemGroup>

  <!-- 保留必要的程序集 -->
  <ItemGroup>
    <TrimmerRootDescriptor Include="ILLink.Descriptors.xml" Condition="Exists('ILLink.Descriptors.xml')" />
  </ItemGroup>

</Project>
