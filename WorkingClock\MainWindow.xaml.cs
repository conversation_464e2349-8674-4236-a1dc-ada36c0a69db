﻿using System;
using System.Windows;
using System.Windows.Input;
using System.Windows.Threading;

namespace WorkingClock;

/// <summary>
/// 极简数字时钟主窗口
/// </summary>
public partial class MainWindow : Window
{
    private DispatcherTimer clockTimer = null!;
    private bool is24HourFormat = true;

    public MainWindow()
    {
        InitializeComponent();
        InitializeClock();
    }

    /// <summary>
    /// 初始化时钟
    /// </summary>
    private void InitializeClock()
    {
        clockTimer = new DispatcherTimer();
        clockTimer.Interval = TimeSpan.FromSeconds(1);
        clockTimer.Tick += ClockTimer_Tick;
        clockTimer.Start();
        UpdateTimeDisplay();
    }

    /// <summary>
    /// 时钟定时器事件
    /// </summary>
    private void ClockTimer_Tick(object? sender, EventArgs e)
    {
        UpdateTimeDisplay();
    }

    /// <summary>
    /// 更新时间显示
    /// </summary>
    private void UpdateTimeDisplay()
    {
        var now = DateTime.Now;

        // 更新时间
        if (is24HourFormat)
        {
            TimeTextBlock.Text = now.ToString("HH:mm:ss");
            StatusText.Text = "24H";
        }
        else
        {
            TimeTextBlock.Text = now.ToString("hh:mm:ss tt");
            StatusText.Text = "12H";
        }

        // 更新日期
        DateTextBlock.Text = now.ToString("yyyy年MM月dd日 dddd");
    }

    /// <summary>
    /// 窗口拖动事件
    /// </summary>
    private void Border_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
    {
        if (e.ButtonState == MouseButtonState.Pressed)
        {
            this.DragMove();
        }
    }

    /// <summary>
    /// 置顶菜单点击事件
    /// </summary>
    private void TopMostMenuItem_Click(object sender, RoutedEventArgs e)
    {
        this.Topmost = !this.Topmost;
        TopMostMenuItem.Header = this.Topmost ? "取消置顶" : "置顶";
    }

    /// <summary>
    /// 12小时制菜单点击事件
    /// </summary>
    private void Hour12MenuItem_Click(object sender, RoutedEventArgs e)
    {
        is24HourFormat = false;
        UpdateTimeDisplay();
    }

    /// <summary>
    /// 24小时制菜单点击事件
    /// </summary>
    private void Hour24MenuItem_Click(object sender, RoutedEventArgs e)
    {
        is24HourFormat = true;
        UpdateTimeDisplay();
    }

    /// <summary>
    /// 关于菜单点击事件
    /// </summary>
    private void AboutMenuItem_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("极简数字时钟 v1.0\nWindows 11 现代化数字时钟应用\n\n功能特色：\n• 实时时间显示\n• 12/24小时制切换\n• 窗口置顶功能\n• 拖动移动窗口\n• 半透明现代化界面",
                       "关于",
                       MessageBoxButton.OK,
                       MessageBoxImage.Information);
    }

    /// <summary>
    /// 退出菜单点击事件
    /// </summary>
    private void ExitMenuItem_Click(object sender, RoutedEventArgs e)
    {
        Application.Current.Shutdown();
    }
}